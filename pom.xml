<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zhongyixin</groupId>
    <artifactId>sinosig-resale-platform</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>sinosig-resale-biz</module>
        <module>sinosig-resale-portal</module>
        <module>sinosig-resale-admin</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.0</revision>
        <spring-boot.version>3.2.10</spring-boot.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <ext.version>3.2.10-feature-r5</ext.version>
        <hutool.version>5.8.27</hutool.version>
        <lombok.version>1.18.28</lombok.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <druid-spring.version>1.2.23</druid-spring.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <easyexcel.verion>4.0.3</easyexcel.verion>
        <doc.version>4.5.0</doc.version>
        <kaptcha.version>2.3.2</kaptcha.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- spring boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <!-- 私库 -->
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>common-core</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-pageable-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-exception-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-http-client-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-jackson-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-namespace-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-signature-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-opt-log-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-sms-client-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-log-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-file-biz-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-file-manager-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-mybatis-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-cache-redisson-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-lock-redisson-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-auth-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>


            <dependency>
                <groupId>com.wftk</groupId>
                <artifactId>wftk-message-spring-boot-starter</artifactId>
                <version>${ext.version}</version>
            </dependency>

            <!-- 外部库 -->

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>


            <!--数据库连接-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <!--阿里数据库连接池-->
            <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-3-starter -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid-spring.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.github.xiaoymin/knife4j-openapi3-jakarta-spring-boot-starter -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${doc.version}</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>


            <!-- 工具类相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-captcha</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.15.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-mock</artifactId>
                <version>2.0.8</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <compilerArgument>-parameters</compilerArgument>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                        <!-- 打包通过systemScope引入的jar包 -->
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>


        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>



</project>
