package com.zhongyixin.resale.portal.loader;

import com.wftk.signature.builder.ClientInfo;
import com.wftk.signature.loader.ClientCredentialLoader;

public class DatabaseClientCredentialLoader implements ClientCredentialLoader {
    @Override
    public ClientInfo get(String clientId) {
//        ClientSetting clientSetting = clientSettingService.getByClientId(clientId);
//        if (clientSetting == null) {
//            return null;
//        }
        ClientInfo clientInfo = new ClientInfo();
//        clientInfo.setClientId(clientSetting.getClientId());
//        clientInfo.setClientSecret(clientSetting.getClientSecret());
        return clientInfo;
    }
}
