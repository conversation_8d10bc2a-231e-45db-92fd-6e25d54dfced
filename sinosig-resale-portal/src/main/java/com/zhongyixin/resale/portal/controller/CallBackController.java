package com.zhongyixin.resale.portal.controller;

import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import com.zhongyixin.resale.portal.annotation.namespace.CallbackMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@CallbackMapping
public class CallBackController {

//    @Autowired
//    LianLianPayService lianLianPayService;
//
//    @PostMapping("/notifyMessage")
//    public LianLianPayCallbackRespOutput receiveLianlianpayNotify(@RequestBody PaymentNoticeInput notifyInput){
//        log.info("lian lian pay callback message is {}",notifyInput);
//        return lianLianPayService.disposeCallback(notifyInput);
//    }

}
