package com.zhongyixin.resale.portal.controller;

import com.sinosig.ec.security.Base64;
import com.sinosig.ec.security.RSADecrypter;
import com.wftk.common.core.result.ApiResult;
import com.wftk.jackson.core.JSONObject;
import com.zhongyixin.resale.biz.common.enums.ErrorCodeEnum;
import com.zhongyixin.resale.biz.common.event.OrderCreateSucceeEvent;
import com.zhongyixin.resale.biz.common.exception.SinosigApiBadBizException;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoQueryVO;
import com.zhongyixin.resale.biz.common.result.SinosigResult;
import com.zhongyixin.resale.biz.common.util.AESUtil;
import com.zhongyixin.resale.biz.common.util.DES;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderReceiveDTO;
import com.zhongyixin.resale.biz.ext.sinosig.output.SinosigOrderCreateOutput;
import com.zhongyixin.resale.biz.service.SinosigNotifyService;
import com.zhongyixin.resale.biz.service.SunshineBusinessService;
import com.zhongyixin.resale.biz.service.SunshineOrderService;
import com.zhongyixin.resale.portal.annotation.namespace.SinosigMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @create 2024/3/6 11:20
 */


@Slf4j
@SinosigMapping
@Tag(name = "阳光api")
public class SinosigController {


    @Autowired
    SinosigNotifyService sinosigNotifyService;

    @Autowired
    SunshineOrderService sunshineOrderService;

    @Autowired
    SunshineBusinessService sunshineBusinessService;

    @Autowired
    ApplicationContext applicationContext;

    @Value("ext.secret")
    private String extSecret;
    @Value("ext.ins.secret")
    private String insSecret;

    @Operation(summary= "下单")
    @PostMapping("/createOrder")
    public SinosigOrderCreateOutput pay(@Validated @RequestBody SinosigOrderReceiveDTO sinosigOrderPushDTO){
        SinosigNotifyOrderReceiveDTO sinosigNotifyOrderReceiveDTO = sinosigNotifyService.receiveSinosigOrder(sinosigOrderPushDTO);
        // 判断是否发送事件
        if(sinosigNotifyOrderReceiveDTO.isSendEventEnabled()){
            //广播订单创建成功
            applicationContext.publishEvent(new OrderCreateSucceeEvent(sinosigNotifyOrderReceiveDTO.getOrder(),
                    sinosigNotifyOrderReceiveDTO.getServiceOrder(),sinosigNotifyOrderReceiveDTO.getOrgName()));
        }
        return sinosigNotifyOrderReceiveDTO.getSinosigOrderCreateOutput();
    }

    @Operation(summary = "修改订单")
    @PostMapping("updateOrder")
    public SinosigResult update(@RequestBody SinosigDTO dto){
        log.info("sinosig update order params:{}", dto);
        sunshineOrderService.updateOrder(decrypt(dto, UpdateOrderDTO.class));
        return SinosigResult.ok();
    }



    @PostMapping("/querySinosigPoint")
    public ApiResult<SunshineUserPointInfoQueryVO> querySinosigPoint(@Validated @RequestBody SunshineUserInfoQueryDTO sunshineUserInfoQueryDTO){
        return sunshineBusinessService.querySinosigOrderPointLogicDeal(sunshineUserInfoQueryDTO);
    }


    @PostMapping("/receiveCardRolls")
    public ApiResult<String> receiveCardRolls(@Validated @RequestBody SunshineReceiveCardRollsDTO sunshineReceiveCardRollsDTO){
        return ApiResult.ok(sunshineBusinessService.receiveCardRolls(sunshineReceiveCardRollsDTO));
    }


    public <T> T decrypt(SinosigDTO dto, Class<T> clz) {
        String sendInfo = dto.getSendInfo();

        //请求参数
        if (sendInfo == null) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.SEND_INFO_BLANK.getCode(), ErrorCodeEnum.SEND_INFO_BLANK.getMessage());
        }
        JSONObject instance = JSONObject.getInstance();


        String[] split = sendInfo.split("\\|");

        String params;
        try {
            RSADecrypter dec = new RSADecrypter(extSecret);
            byte[] decrypt = dec.decrypt(Base64.decode(split[0]));
            String key = new String(decrypt, StandardCharsets.UTF_8);
            params = DES.decrypt(split[1], key);
            log.info("decrypt success params:{}",params);
        }catch (Exception e){
            log.error(e.getClass().getName(),e);
            throw new SinosigApiBadBizException(ErrorCodeEnum.PARAM_DECRYPT_ERROR.getCode(), ErrorCodeEnum.PARAM_DECRYPT_ERROR.getMessage());
        }

        String signParams = null;
        String sign = null;
        T t;
        try {
            t = instance.parseObject(params, clz);
            if(t instanceof UpdateOrderDTO orderDTO){
                sign = orderDTO.getSign();
                signParams = (orderDTO.getOrderId()+orderDTO.getModifyToName()+orderDTO.getModifyToIdNo());
            }else if(t instanceof CancelOrderDTO orderDTO){
                sign = orderDTO.getSign();
                signParams = orderDTO.getOrderId();
            }

        }catch (Exception e){
            log.error(e.getClass().getName(),e);
            throw new SinosigApiBadBizException(ErrorCodeEnum.PARAM_INVALID.getCode(), ErrorCodeEnum.PARAM_INVALID.getMessage());
        }
        if(sign == null){
            throw new SinosigApiBadBizException(ErrorCodeEnum.SIGN_BLANK.getCode(),ErrorCodeEnum.SIGN_BLANK.getMessage());
        }

        if(!AESUtil.encrypt(signParams,insSecret.substring(0,16)).equals(sign)){
            log.info("sign Verify fail:{}", AESUtil.encrypt(signParams,insSecret));
            throw new SinosigApiBadBizException(ErrorCodeEnum.SIGN_VERIFY_FAIL.getCode(),ErrorCodeEnum.SIGN_VERIFY_FAIL.getMessage());
        }
        return t;
    }





}
