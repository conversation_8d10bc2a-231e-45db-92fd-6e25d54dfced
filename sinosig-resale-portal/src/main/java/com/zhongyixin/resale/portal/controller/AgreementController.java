//package com.zhongyixin.resale.portal.controller;
//
//import com.wftk.common.core.result.ApiResult;
//import com.zhongyixin.resale.portal.annotation.namespace.AgreementMapping;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Parameters;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.List;
//
//@AgreementMapping
//@Tag(name = "业务端-协议相关api")
//public class AgreementController {
//
//
//    @Autowired
//    private AgreementService agreementService;
//
//    @GetMapping
//    @Parameters({
//            @Parameter(name = "source", description = "来源(1、)", required = true)
//    })
//    public ApiResult<List<AgreementTagOutput>> getAgreement(@RequestParam("source")Integer source) {
//        return ApiResult.ok(agreementService.listBySource(source));
//    }
//
//    @GetMapping("/qetById")
//    public ApiResult<Agreement> getAgreementById(@RequestParam("id")Long id) {
//        return ApiResult.ok(agreementService.getById(id));
//    }
//}
