package com.zhongyixin.resale.portal.annotation.namespace;

import com.wftk.namespace.spring.boot.autoconfigure.annotation.NamespaceRequestMapping;
import com.zhongyixin.resale.portal.annotation.constant.NamespaceConstant;
import org.springframework.core.annotation.AliasFor;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@NamespaceRequestMapping
@RestController
public @interface CallbackMapping {

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String namespace() default NamespaceConstant.CALLBACK;

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] value() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    String[] path() default {};

    @AliasFor(annotation = NamespaceRequestMapping.class)
    RequestMethod[] method() default {};
}
