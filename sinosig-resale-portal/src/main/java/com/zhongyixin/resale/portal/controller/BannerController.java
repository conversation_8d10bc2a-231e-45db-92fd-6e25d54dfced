//package com.zhongyixin.resale.portal.controller;
//
//import com.chili.vas.biz.constant.BannerConstant;
//import com.chili.vas.biz.converter.BannerConverter;
//import com.chili.vas.biz.entity.Banner;
//import com.chili.vas.biz.output.portal.BannerOutput;
//import com.chili.vas.biz.service.BannerService;
//import com.chili.vas.portal.annotation.namespace.PortalBannerMapping;
//import com.wftk.common.core.result.ApiResult;
//import com.zhongyixin.resale.portal.annotation.namespace.BannerMapping;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @createDate 2025/4/28 17:12
// */
//@BannerMapping
//@Tag(name = "业务端-banner相关api")
//public class BannerController {
//
//    @Autowired
//    private BannerService bannerService;
//
//    @Autowired
//    private BannerConverter bannerConverter;
//
//    @GetMapping("/queryBanner")
//    public ApiResult<List<BannerOutput>> queryBanner(@RequestParam("type") Integer type, @RequestParam("source") Integer source){
//        List<Banner> banners = bannerService.selectListByType(BannerConstant.STATUS_NOMAL,source , type);
//        return ApiResult.ok(bannerConverter.entityToOutput(banners));
//    }
//
//    @GetMapping("/queryAllBanner")
//    public ApiResult<List<BannerOutput>> queryAllBanner(@RequestParam("source") Integer source){
//        List<Banner> banners  = bannerService.selectListByStatus(BannerConstant.STATUS_NOMAL, source);
//        List<BannerOutput> bannerOutputs = bannerConverter.entityToOutput(banners);
//        return ApiResult.ok(bannerOutputs);
//    }
//
//    @GetMapping("/queryBannerByTypes")
//    public ApiResult<List<BannerOutput>> queryBannerByTypes(@RequestParam("type") List<Integer> type,@RequestParam("source") Integer source){
//        List<Banner> banners = bannerService.selectListByTypes(BannerConstant.STATUS_NOMAL, source,type);
//        List<BannerOutput> bannerOutputs = bannerConverter.entityToOutput(banners);
//        return ApiResult.ok(bannerOutputs);
//    }
//
//}
