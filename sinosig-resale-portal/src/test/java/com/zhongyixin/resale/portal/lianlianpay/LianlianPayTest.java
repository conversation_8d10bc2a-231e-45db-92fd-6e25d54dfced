package com.zhongyixin.resale.portal.lianlianpay;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.ext.lianlianpay.dto.PaymentDTO;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import com.zhongyixin.resale.portal.SinosigResalePortalApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <AUTHOR>
 * @create 2023/2/6 11:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SinosigResalePortalApplication.class)
@Slf4j
public class LianlianPayTest {

    @Autowired
    private LianlianPayApiManager lianlianPayApiManager;


    @Autowired
    HttpRequestExecutor httpRequestExecutor;



    @Test
    public void testLianlianPayment(){
        //发起代付   0.1*4
        //发送连连代付请求
        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setAmount("0.1");
        paymentDTO.setBankCardNo("6236682950005071621");
        paymentDTO.setCardName("魏加");
        paymentDTO.setOrderId("*********");
        paymentDTO.setPlatform("RONGZHI");
        paymentDTO.setInfoOrder(LianlianPayConstant.QUNQIU_INFO_ORDER);
        try {
            PaymentOut paymentOut = lianlianPayApiManager.payment(paymentDTO);
            System.out.println(paymentOut.successed());
            System.out.println(paymentOut);
        } catch (Exception e) {
            log.error("lianlian payment 接口异常" , e);
        }
    }


    @Test
    public void retryLianlianPayment(){
        //发送连连代付请求
//        PaymentDTO paymentDTO = new PaymentDTO();
//        paymentDTO.setAmount("1306.00");
//        paymentDTO.setBankCardNo("6212252307000306901");
//        paymentDTO.setCardName("刘洁");
//        paymentDTO.setOrderId("1803767100608446464");
//        paymentDTO.setPlatform("RONGZHI");
//        paymentDTO.setInfoOrder(LianlianPayConstant.QUNQIU_INFO_ORDER);
//        try {
//            PaymentOut paymentOut = lianlianPayApiManager.payment(paymentDTO);
//            System.out.println(paymentOut.successed());
//            System.out.println(paymentOut);
//        } catch (Exception e) {
//            log.error("lianlian payment 接口异常:" + e.getMessage());
//        }
    }

    public static void main(String[] args) {
        // 连连参数
//        LianlianPayParamsDTO lianlianPayParamsDTO = new LianlianPayParamsDTO();
//        lianlianPayParamsDTO.setInfoOrder("备付金");
//        lianlianPayParamsDTO.setFlagCard("0");
//        lianlianPayParamsDTO.setMemo("银联入账");
//        lianlianPayParamsDTO.setNotifyUrl("http://**************:18500/api/callback/notifyMessage");
//        lianlianPayParamsDTO.setOidPartner("201912170937031054");
//        lianlianPayParamsDTO.setApiVersion("1.0");
//        lianlianPayParamsDTO.setSignType("RSA");
//        lianlianPayParamsDTO.setPrivateKey("MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOilN4tR7HpNYvSBra/DzebemoAiGtGeaxa+qebx/O2YAdUFPI+xTKTX2ETyqSzGfbxXpmSax7tXOdoa3uyaFnhKRGRvLdq1kTSTu7q5s6gTryxVH2m62Py8Pw0sKcuuV0CxtxkrxUzGQN+QSxf+TyNAv5rYi/ayvsDgWdB3cRqbAgMBAAECgYEAj02d/jqTcO6UQspSY484GLsL7luTq4Vqr5L4cyKiSvQ0RLQ6DsUG0g+Gz0muPb9ymf5fp17UIyjioN+ma5WquncHGm6ElIuRv2jYbGOnl9q2cMyNsAZCiSWfR++op+6UZbzpoNDiYzeKbNUz6L1fJjzCt52w/RbkDncJd2mVDRkCQQD/Uz3QnrWfCeWmBbsAZVoM57n01k7hyLWmDMYoKh8vnzKjrWScDkaQ6qGTbPVL3x0EBoxgb/smnT6/A5XyB9bvAkEA6UKhP1KLi/ImaLFUgLvEvmbUrpzY2I1+jgdsoj9Bm4a8K+KROsnNAIvRsKNgJPWd64uuQntUFPKkcyfBV1MXFQJBAJGs3Mf6xYVIEE75VgiTyx0x2VdoLvmDmqBzCVxBLCnvmuToOU8QlhJ4zFdhA1OWqOdzFQSw34rYjMRPN24wKuECQEqpYhVzpWkA9BxUjli6QUo0feT6HUqLV7O8WqBAIQ7X/IkLdzLa/vwqxM6GLLMHzylixz9OXGZsGAkn83GxDdUCQA9+pQOitY0WranUHeZFKWAHZszSjtbe6wDAdiKdXCfig0/rOdxAODCbQrQs7PYy1ed8DuVQlHPwRGtokVGHATU=");
//        lianlianPayParamsDTO.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCSS/DiwdCf/aZsxxcacDnooGph3d2JOj5GXWi+q3gznZauZjkNP8SKl3J2liP0O6rU/Y/29+IUe+GTMhMOFJuZm1htAtKiu5ekW0GlBMWxf4FPkYlQkPE0FtaoMP3gYfh+OwI+fIRrpW3ySn3mScnc6Z700nU/VYrRkfcSCbSnRwIDAQAB");
//        lianlianPayParamsDTO.setHost("https://test.lianlianpay-inc.com/paymentapi");

        //民生参数
//        CmbcParamsDTO cmbcParamsDTO = new CmbcParamsDTO();
//        cmbcParamsDTO.setNotifyUrl("http://**************:18500/api/callback/cmbc/openapi/notify");
//        cmbcParamsDTO.setContractId("01202301101034310003");
//        cmbcParamsDTO.setBizType("2006");
//        cmbcParamsDTO.setPostscript("备付金");
//        cmbcParamsDTO.setSummary("备付金");
//        cmbcParamsDTO.setCurrencyCode("RMB");
//        cmbcParamsDTO.setDefaultTradeType("30010002");
//        cmbcParamsDTO.setPayeeAccType("1");
//        cmbcParamsDTO.setPayeePartyId("305100000013");

//        System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(cmbcParamsDTO));
    }

}
