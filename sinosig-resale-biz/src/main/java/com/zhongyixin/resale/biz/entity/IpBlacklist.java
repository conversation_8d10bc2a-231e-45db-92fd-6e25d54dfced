package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * IP黑名单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_ip_blacklist")
public class IpBlacklist implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 类型
     */
    private String apiType;

    /**
     * 状态(1.正常 -1.删除)
     */
    private Integer status;

    /**
     * 解除时间
     */
    private LocalDateTime relieveTime;

    /**
     * 解除人
     */
    private String reliever;


}
