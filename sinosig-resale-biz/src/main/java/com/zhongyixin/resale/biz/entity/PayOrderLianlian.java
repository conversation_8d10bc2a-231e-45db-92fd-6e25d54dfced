package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_pay_order_lianlian")
public class PayOrderLianlian implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 支付订单id
     */
    private String payOrderId;

    /**
     * 平台订单号
     */
    private String paymentOrderNo;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 乐享语驾卡号
     */
    private String couponNo;

    /**
     * 第三方渠道订单id
     */
    private String cardOrderNo;

    /**
     * 用户姓名
     */
    private String cardName;

    /**
     * 手机号
     */
    private String mobileNumber;

    /**
     * 保单后六位
     */
    private String insurancestr;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 金额
     */
    private BigDecimal moneyOrder;

    /**
     * 状态（0.开始支付 1.支付中 2.支付成功 9.支付失败）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 来源
     */
    private String apiSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 清算时间
     */
    private String settleDate;

    /**
     * 订单描述
     */
    private String infoOrder;

    /**
     * 订单摘要
     */
    private String memo;

    /**
     * 备用字段
     */
    private String ext1;

    /**
     * 乐观锁
     */
    private Integer version;


}
