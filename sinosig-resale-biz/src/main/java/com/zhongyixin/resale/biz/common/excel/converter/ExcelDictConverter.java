package com.zhongyixin.resale.biz.common.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.zhongyixin.resale.biz.common.enums.ExcelStateConverter;
import com.zhongyixin.resale.biz.common.excel.annotation.ExcelConverterType;

import java.lang.reflect.Field;


public class ExcelDictConverter implements Converter<Integer> {



    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

        Field field = contentProperty.getField();
        if(null==value) {
            return new WriteCellData<>();
        }
        String type = field.getAnnotation(ExcelConverterType.class).type();

        return new WriteCellData<>(ExcelStateConverter.getValue(type, value));
    }


}
