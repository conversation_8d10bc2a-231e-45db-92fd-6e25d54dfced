package com.zhongyixin.resale.biz.ext.sinosig.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 16:28
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SinosigOrderReceiveDTO {

    /**
     * 渠道编码
     */
    @NotBlank(message = "渠道编码不能为空")
    private String source;

    /**
     * 阳光配置的第三方机构
     */
    @NotBlank(message = "第三方机构不能为空")
    private String apiSource;

    /**
     * 客户号
     */
    private String customerNo;

    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 客户身份证号
     */
    private String idCard;

    /**
     * 服务生效时间
     */
    @NotNull(message = "服务生效时间不能为空")
    @JsonProperty("service_eff_date")
    private LocalDateTime serviceEffDate;

    /**
     * 服务失效时间
     */
    @NotNull(message = "服务失效时间不能为空")
    @JsonProperty("service_exp_date")
    private LocalDateTime serviceExpDate;

    /**
     * 机构code
     */
    @NotBlank(message = "机构code不能为空")
    @JsonProperty("org_code")
    private String orgCode;

    /**
     * 机构名称
     */
    @NotBlank(message = "机构名称不能为空")
    @JsonProperty("org_name")
    private String orgName;

    /**
     * 机构父code
     */
    @NotBlank(message = "机构父code不能为空")
    @JsonProperty("org_parent_code")
    private String orgParentCode;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    /**
     * 供应商Id
     */
    @NotBlank(message = "供应商Id不能为空")
    private String ppdid;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String providerName;

    /**
     * 金额
     */
    @NotBlank(message = "金额不能为空")
    private String amount;


    /**
     * 代理人编码
     */
    private String agentCode;

    /**
     * 代理人名称
     */
    private String agentName;

    /**
     * 签名类型
     */
    @NotBlank(message = "签名类型不能为空")
    private String signType;

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;


}
