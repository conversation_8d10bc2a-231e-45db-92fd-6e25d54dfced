package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface PayOrderLianlianMapper extends BaseMapper<PayOrderLianlian> {

    /**
     * 根据支付Id查询连连订单
     * @param payOrderId
     * @return
     */
    PayOrderLianlian selectPayOrderLianlianByPayOrderId(String payOrderId);

    /**
     * 根据支付Id修改民生订单信息
     * @param payOrderLianlian
     * @return
     */
    Integer updatePayOrderLianlianByPayOrderId(PayOrderLianlian payOrderLianlian);

    /**
     * 根据订单ID查询未完成的订单
     * @param orderId
     * @return
     */
    PayOrderLianlian selectUnfinishedOrderByOrderId(@Param("orderId") String orderId);

    /**
     * 根据订单ID查询支付中或者已支付的订单
     * @param orderId
     * @return
     */
    PayOrderLianlian selectPayingOrPaidOrderByOrderId(@Param("orderId") String orderId);

    /**
     * 查询10分钟前的订单
     * @return
     */
    List<PayOrderLianlian> selectOrderByTenMinutes();
}
