package com.zhongyixin.resale.biz.common.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/6 14:15
 */

@Getter
public class SinosigApiBadBizException extends RuntimeException {

    private final String code;


    public SinosigApiBadBizException(String code) {
        this.code = code;
    }

    public SinosigApiBadBizException(String code, String message) {
        super(message);
        this.code = code;
    }

    public SinosigApiBadBizException(Throwable cause, String code) {
        super(cause);
        this.code = code;
    }

    public SinosigApiBadBizException(String message, Throwable cause, String code) {
        super(message, cause);
        this.code = code;
    }


}
