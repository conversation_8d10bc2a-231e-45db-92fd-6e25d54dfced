package com.zhongyixin.resale.biz.ext.lianlianpay.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.zhongyixin.resale.biz.ext.lianlianpay.bean.PaymentRequest;
import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.ext.lianlianpay.dto.PaymentDTO;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentInput;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.QueryPaymentInput;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.QueryPaymentOut;
import com.zhongyixin.resale.biz.ext.lianlianpay.security.LLianPayYtSignature;
import com.zhongyixin.resale.biz.properties.LianlianPayQunqiuProperties;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023/2/24 17:18
 */
@Slf4j
public class DefaultLianlianPayApiManager implements LianlianPayApiManager{
    protected final HttpRequestExecutor requestExecutor;

    protected final String oidPartner;

    protected final String publicKeyOnline;

    protected final String businssPrivateKey;

    protected final String domain;

    protected final String callback;



    public DefaultLianlianPayApiManager(HttpRequestExecutor httpRequestExecutor, LianlianPayQunqiuProperties lianlianPayProperties) {
        this.requestExecutor = httpRequestExecutor;
        this.oidPartner = lianlianPayProperties.getOidPartner();
        this.publicKeyOnline = lianlianPayProperties.getPublicKeyOnline();
        this.businssPrivateKey = lianlianPayProperties.getBusinssPrivateKey();
        this.domain = lianlianPayProperties.getDomain();
        this.callback = lianlianPayProperties.getCallback();
    }


    /**
     * 实时付款接口
     * @param paymentDTO 用户信息
     * @return response
     */
    @Override
    public PaymentOut payment(PaymentDTO paymentDTO) throws Exception {
        PaymentRequest paymentRequestBean = new PaymentRequest();
        paymentRequestBean.setNo_order(paymentDTO.getOrderId());
        String dtOrder = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        paymentRequestBean.setDt_order(dtOrder);
        paymentRequestBean.setMoney_order(paymentDTO.getAmount());
        paymentRequestBean.setCard_no(paymentDTO.getBankCardNo());
        paymentRequestBean.setAcct_name(paymentDTO.getCardName());
        paymentRequestBean.setInfo_order(paymentDTO.getInfoOrder());
        paymentRequestBean.setFlag_card(LianlianPayConstant.FLAG_CARD);
        paymentRequestBean.setMemo(LianlianPayConstant.MEMO);
        // 填写商户自己的接收付款结果回调异步通知
        paymentRequestBean.setNotify_url(callback);
        paymentRequestBean.setOid_partner(oidPartner);
        paymentRequestBean.setPlatform(paymentDTO.getPlatform());
        paymentRequestBean.setApi_version(LianlianPayConstant.API_VERSION);
        paymentRequestBean.setSign_type(LianlianPayConstant.SIGN_TYPE);

        // 用商户自己的私钥加签
        paymentRequestBean.setSign(LLianPayYtSignature.getInstance().sign(businssPrivateKey,BeanUtil.beanToMap(paymentRequestBean)));

        String jsonStr = JSONObject.getInstance().toJSONString(paymentRequestBean);
        log.info("lianlianpay [payment] 请求报文：" + jsonStr);
        String encryptStr = LLianPayYtSignature.getInstance().encryptGeneratePayload(jsonStr, publicKeyOnline);

        if (StrUtil.isBlank(encryptStr)) {
            // 加密异常
            log.error("lianlianpay [payment] 加密异常! encryptStr: {}",encryptStr);
            throw new BusinessException("lianlianpay [payment] 加密异常!");
        }

        PaymentInput paymentInput = new PaymentInput();
        paymentInput.setOid_partner(oidPartner);
        paymentInput.setPay_load(encryptStr);
        String serverUrl = getUrl(LianlianPayConstant.API.APPLY_PAYMENT);
        HttpRequest<PaymentInput, PaymentOut> httpRequest = RequestBuilders.<PaymentInput, PaymentOut>bodyBuilder(serverUrl)
                .method(RequestMethod.POST)
                .json(paymentInput)
                .resultType(new DataType<>() {
                })
                .build();

        PaymentOut paymentOut = execute(httpRequest);
        log.info("lianlianpay [payment] 响应报文：{}",paymentOut);
        return paymentOut;
    }

    /**
     * 实时付款查询接口
     * @param orderNo 订单id
     * @return response
     */
    @Override
    public QueryPaymentOut queryPayment(String orderNo) {
        QueryPaymentInput queryPaymentInput = new QueryPaymentInput();
        queryPaymentInput.setNo_order(orderNo);
        queryPaymentInput.setOid_partner(oidPartner);
        queryPaymentInput.setApi_version(LianlianPayConstant.API_VERSION);
        queryPaymentInput.setSign_type(LianlianPayConstant.SIGN_TYPE);
        queryPaymentInput.setSign(LLianPayYtSignature.getInstance().sign(businssPrivateKey,BeanUtil.beanToMap(queryPaymentInput)));

        String serverUrl = getUrl(LianlianPayConstant.API.QUERY_PAYMENT);
        HttpRequest<QueryPaymentInput, QueryPaymentOut> httpRequest = RequestBuilders.<QueryPaymentInput, QueryPaymentOut>bodyBuilder(serverUrl)
                .method(RequestMethod.POST)
                .json(queryPaymentInput)
                .resultType(new DataType<>() {
                })
                .build();

        QueryPaymentOut queryPaymentOut = execute(httpRequest);
        log.info("lianlianpay [queryPayment] 响应报文：{}",queryPaymentOut);
        return queryPaymentOut;
    }

    /**
     * 接收通知接口验签
     * @param jsonStr 对象json String
     * @param sign 签名
     * @return response
     */
    @Override
    public Boolean verifySign(String jsonStr, String sign) {
        return LLianPayYtSignature.getInstance().checkSign(publicKeyOnline,
                LLianPayYtSignature.getInstance().sign(businssPrivateKey,JSONObject.getInstance().parseMap(jsonStr,String.class,Object.class)),
                sign);
    }

    protected String getUrl(String api) {
        return URLUtil.normalize(domain + api);
    }

    protected <P, R> R execute(HttpRequest<P, R> httpRequest) {
        return requestExecutor.execute(httpRequest);
    }
}
