package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.manage.SunshineOrderManager;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 支付服务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    @Resource
    OrderService orderService;

    @Resource
    ServiceOrderService serviceOrderService;


    @Resource
    PayOrderLianlianService payOrderLianlianService;

    @Resource
    IntefaceInfoService intefaceInfoService;


    @Resource
    LianlianPayApiManager lianlianPayApiManager;

    @Resource
    ApplicationContext applicationContext;

    @Resource
    SunshineOrderManager sunshineOrderManager;

    @Resource
    PayRecordService payRecordService;

    @Resource
    LockManager lockManager;


    @Override
    public void sendLianlianPaymentRequest(Order order, String payOrderId, ServiceOrder serviceOrder) {

    }

    @Override
    public void sendNewLianlianPaymentRequest(Order order, String payOrderId, ServiceOrder serviceOrder) {

    }

    @Override
    public void sendLianlianPaymentRequest(Order order, String payOrderId) {

    }

    @Override
    public void paymentSuccessDealLogic(Order order, LocalDateTime dateTime) {

    }

    @Override
    public void orderQueryLianlianPayApi(PayOrderLianlian payOrderLianlian) {

    }

    @Override
    public void publishOrderXConsumeSuccessEvent(Order order, String orderId) {

    }

    @Override
    public void sinosigOrganizeQuota(Order order, ServiceOrder serviceOrder, String payOrderId, String payWay, String payCompany) {

    }
}
