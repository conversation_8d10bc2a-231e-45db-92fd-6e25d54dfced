package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.common.input.NonClosedLoopPayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.input.SunshinePayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.output.NonClosedLoopPayRecordVo;
import com.zhongyixin.resale.biz.common.output.SunshinePayRecordVo;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayRecord;

/**
 * <p>
 * 支付记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface PayRecordService extends IService<PayRecord> {

    Boolean insertPayRecord(Order order , int status , String payOrderId, String payCompany, String tranResult);

    PayRecord selectLastTimeOne(String orderId,Integer status);

}
