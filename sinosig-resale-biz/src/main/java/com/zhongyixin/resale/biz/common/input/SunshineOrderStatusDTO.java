package com.zhongyixin.resale.biz.common.input;

import com.zhongyixin.resale.biz.common.sensitive.Sensitive;
import com.zhongyixin.resale.biz.common.sensitive.SensitiveStrategy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/5/14 14:13
 */
@Data
public class SunshineOrderStatusDTO {

    private Integer id;

    private String orderId;

    private String couponNo;

    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String telephone;

    @Sensitive(strategy = SensitiveStrategy.USERNAME)
    private String userName;

    private String bankCardNo;

    private BigDecimal amount;

    private LocalDateTime createTime;

    private LocalDateTime tranDate;

    private String status;

    private String payCompany;


}
