package com.zhongyixin.resale.biz.common.input;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2023/2/21 8:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizeSummarizeDTO extends PaginationDTO{

    /**
     * 创建起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate createStartDate;

    /**
     * 创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate createEndDate;

    /**
     * 核销起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate tranStartDate;

    /**
     * 核销结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate tranEndDate;

    /**
     * 月份
     */
    private String month;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 核销状态
     */
    private Integer repayStatus = 2;

    /**
     * 文件名
     */
    private String fileName;
}
