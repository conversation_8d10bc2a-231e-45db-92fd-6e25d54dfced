package com.zhongyixin.resale.biz.common.constant;

public interface OrderWriteOffConstant {

    interface OrderStatus{

        /**
         * 待核销
         */
        Integer WAIT = 0;

        /**
         * 核销中
         */
        Integer PROCESS = 1;

        /**
         * 核销成功
         */
        Integer SUCCESS = 2;

        /**
         * 核销失败
         */
        Integer FAIL = 3;

        /**
         * 核销异常
         */
        Integer UNEXPECTED = 4;

    }

    interface VasCouponStatus{

        /**
         * 待核销
         */
        Integer WAIT = 0;

        /**
         * 核销中
         */
        Integer PROCESS = 1;

        /**
         * 核销成功
         */
        Integer SUCCESS = 2;

        /**
         * 核销失败
         */
        Integer FAIL = 3;

        /**
         * 核销异常
         */
        Integer UNEXPECTED = 4;

    }

}
