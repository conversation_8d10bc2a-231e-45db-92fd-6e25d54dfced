package com.zhongyixin.resale.biz.common.event.Listener;

import com.wftk.jackson.core.JSONObject;
import com.zhongyixin.resale.biz.common.event.OrderCreateSucceeEvent;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.service.OrganizeYgryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderCreateSuccessListener implements ApplicationListener<OrderCreateSucceeEvent> {


    @Resource
    OrganizeYgryService organizeYgryService;

    @Async
    @Override
    public void onApplicationEvent(OrderCreateSucceeEvent event) {
        log.info("order create success event. source is {}", JSONObject.getInstance().toJSONString(event));
        ServiceOrder serviceOrder = event.getServiceOrder();

        //新增机构
        try{
            organizeYgryService.createOrganize(serviceOrder, event.getOrgName());
        }catch (Exception e){
            log.error("order init. organize create fail. orderId is {}",
                    serviceOrder.getOrderId(),e);
        }
    }
}
