package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 渠道通道配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChannelSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 状态 1.正常 2.维护
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 描述
     */
    private String description;

    /**
     * 删除时间
     */
    private Integer deleted;


}
