package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 * @create 2024/3/11 10:31
 */
public interface NotifyJobConstant {




    String NOTIFY_JOB_REDIS_KEY = "timejob:notify:fail_request:retry:ten_second";

    String SAVE_NOTIFY_JOB_REDIS_KEY = "lock:notifyJob:save:";

    Integer COUNT = 5;

    String SUCCESS_RESULT_CODE = "0000";

    interface BusinessCode{
        String CHHPLAT_ORDER_INIT = "chhplat_order_init";
        String CHHPLAT_ORDER_CONSUME = "chhplat_order_consume";
        String CRM_ORDER_CONSUME = "crm_order_consume";

        /**
         * 核销推送
         */
        String SINOSIG_CASH_NOTIFY = "sinosig_cash_notify";

        /**
         * 卡券推送
         */
        String SINOSIG_COUPON_CASH_NOTIFY = "sinosig_coupon_cash_notify";

        /**
         * 卡券使用推送（订单多个卡券，使用回调多次）
         */
        String SINOSIG_COUPON_USE_CASH_NOTIFY = "sinosig_coupon_use_cash_notify";

        /**
         * 国任订单状态通知
         */
        String GUOREN_ORDER_STATUS_NOTIFY = "guoren_order_status_notify";
    }


    interface Status{
        Integer WAIT = 1;
        Integer SUCCESS = 2;
        Integer FAIL = 10;
    }

    interface Ended{
        Integer YES = 1;
        Integer NO = 0;
    }


    interface ProviderName{
        String A_H_HAI_HUI = "安徽海慧供应链科技有限公司";
    }


}
