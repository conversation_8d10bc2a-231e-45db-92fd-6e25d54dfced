package com.zhongyixin.resale.biz.common.enums;

import java.util.Objects;

public enum ExcelStateConverter {


    TO_BE_REQUESTED("开票清单", 0,"待申请"),
    INVOICING("开票信息管理", 1,"开票中"),
    INVOICE_HAS_BEEN_ISSUED("开票信息管理", 2,"已开票"),
    TO_BE_INVOICED("开票信息批次详情", 1,"待开票"),
    INVOICE_HAS_INVOICED("开票信息批次详情", 2,"已开票"),


    SERVICE_CLOSED_LOOP_ORDER("服务订单模式", 1,"闭环订单"),
    SERVICE_AGENCY_ORDER("服务订单模式", 2,"代理订单"),
    SERVICE_ZHINAJIN("服务订单模式", 3,"滞纳金"),

    ORDER_VERIFY_STATUS_1("订单核销状态",0,"待核销"),
    ORDER_VERIFY_STATUS_2("订单核销状态",1,"已核销"),
    ORDER_VERIFY_STATUS_3("订单核销状态",2,"核销中"),
    ORDER_VERIFY_STATUS_4("订单核销状态",3,"核销异常"),


    ORDER_STATUS_1("订单状态",0,"待投放"),
    ORDER_STATUS_2("订单状态",1,"投放中"),
    ORDER_STATUS_3("订单状态",9,"投放异常"),
    ORDER_STATUS_4("订单状态",3,"投放失败"),
    ORDER_STATUS_5("订单状态",2,"投放成功"),

    DAYSETTLE_STATUS_1("推送状态",1,"待推送 "),
    DAYSETTLE_STATUS_2("推送状态",2,"推送中"),
    DAYSETTLE_STATUS_3("推送状态",3,"推送失败"),
    DAYSETTLE_STATUS_4("推送状态",4,"推送成功"),

    DAYSETTLE_METHOD_1("结算方式",0,"按发放结算"),
    DAYSETTLE_METHOD_2("结算方式",1,"按使用结算");


    private final String type;

    private final Integer key;

    private final String value;

    ExcelStateConverter(String type, int key, String value) {
        this.type = type;
        this.key = key;
        this.value = value;
    }

    public static String getValue(String type,int key) {
        for (ExcelStateConverter dict:values()){
            if(Objects.equals(dict.key, key) && dict.type.equals(type)){
                return dict.value;
            }
        }
        return "";
    }
}
