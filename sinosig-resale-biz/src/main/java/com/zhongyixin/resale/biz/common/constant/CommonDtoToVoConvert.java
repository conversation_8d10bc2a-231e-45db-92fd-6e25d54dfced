package com.zhongyixin.resale.biz.common.constant;

import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.entity.UserOperateInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2023/2/9 17:40
 */
@Mapper
public abstract class CommonDtoToVoConvert {

    public static CommonDtoToVoConvert INSTANCE = Mappers.getMapper(CommonDtoToVoConvert.class);

    /**
     * 订单转用户操作类
     * @param order 订单
     * @return 用户操作类
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    public abstract UserOperateInfo orderToUserOperateInfo(Order order);

    /**
     * 订单转用户操作类
     * @param order 订单
     * @return 用户操作类
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    public abstract Order orderToOrder(Order order);


    /**
     * 订单转连连订单
     * @param order 订单
     * @return 连连订单
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "status",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    @Mapping(source = "telphone",target = "mobileNumber")
    @Mapping(source = "userName",target = "cardName")
    @Mapping(source = "bankCardNo",target = "cardNo")
    @Mapping(source = "amount",target = "moneyOrder")
    public abstract PayOrderLianlian orderToPayOrderLianlian(Order order);





}
