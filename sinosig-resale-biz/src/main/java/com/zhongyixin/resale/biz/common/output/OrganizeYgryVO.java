package com.zhongyixin.resale.biz.common.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2023/9/19 14:21
 */
@Data
public class OrganizeYgryVO {


    /**
     * 0.正常 1.订单不存在 2.机构不存在 3.机构未启用 4.机构维护中
     */
    @Schema(defaultValue = "0.正常 1.订单不存在 2.机构不存在 3.机构未启用 4.机构维护中")
    private Integer status;

    @Schema(defaultValue = "信息")
    private String msg;
}
