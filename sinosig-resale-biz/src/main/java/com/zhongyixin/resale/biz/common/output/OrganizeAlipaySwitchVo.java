package com.zhongyixin.resale.biz.common.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2024/3/20 20:15
 */
@Data
public class OrganizeAlipaySwitchVo {

    @ExcelProperty("机构编码")
    private String orgCode;

    @ExcelIgnore
    private String orgParentCode;

    @ExcelProperty("机构名称")
    private String orgName;

    @ExcelProperty("支付宝开关（1.开 2.关）")
    private Integer alipaySwitch;

}
