package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 总订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_order")
public class Order implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 乐享语驾卡号
     */
    private String couponNo;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 客户类型
     */
    private String orderType;

    /**
     * 第三方渠道订单id
     */
    private String cardOrderNo;

    /**
     * 代付企业
     */
    private String payCompany;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String telphone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 保单号后六位
     */
    private String insurancestr;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 可用积分
     */
    private BigDecimal point;

    /**
     * 兑换金额
     */
    private BigDecimal amount;

    /**
     * 国任锁定积分uuid
     */
    private String uuid;

    /**
     * 订单数量
     */
    private Integer count;

    /**
     * 支付方式(银行卡(后期可以配置)   支付宝（不可配置）   )
     */
    private String payWay;

    /**
     * 状态 0.未支付 1.支付中  2.支付成功  9.支付失败
     */
    private Integer status;

    /**
     * 失败说明
     */
    private String failExplain;

    /**
     * 乐观锁
     */
    private Integer version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 重定向路径
     */
    private String redirectUrl;

    /**
     * 交易时间
     */
    private LocalDateTime tranDate;

    /**
     * 支付商编
     */
    private String paymentNo;


}
