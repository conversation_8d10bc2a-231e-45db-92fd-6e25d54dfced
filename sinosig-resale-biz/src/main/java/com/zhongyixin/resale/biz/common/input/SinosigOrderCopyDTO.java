package com.zhongyixin.resale.biz.common.input;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <p>
 * 阳光明细订单copy表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SinosigOrderCopyDTO {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @ExcelIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    @ColumnWidth(15)
    private String orderId;

    /**
     * 卡密
     */
    @ExcelIgnore
    private String couponNo;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    @ColumnWidth(15)
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    @ExcelProperty("用户姓名")
    @ColumnWidth(15)
    private String userName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @ColumnWidth(15)
    private String telephone;

    /**
     * 银行卡号
     */
    @ExcelIgnore
    private String bankCardNo;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    @ColumnWidth(15)
    private String idCard;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(15)
    private LocalDateTime createTime;

    /**
     * 支付成功时间
     */
    @ExcelProperty("支付成功时间")
    @ColumnWidth(15)
    private LocalDateTime tranDate;

    /**
     * 机构代码
     */
    @ExcelProperty("三级机构代码")
    @ColumnWidth(15)
    private String orgCode;

    /**
     * 父级机构代码
     */
    @ExcelProperty("二级机构代码")
    @ColumnWidth(15)
    private String orgParentCode;

    /**
     * 代付商编
     */
    @ExcelProperty("代付商编")
    @ColumnWidth(15)
    private String paymentNo;

    /**
     * 状态
     */
    @ExcelIgnore
    private Integer status;

    /**
     * 还款状态
     * ,converter = RepayStatusConverter.class
     */
    @ExcelProperty(value = "还款状态")
    @ColumnWidth(15)
    private Integer repayStatus;

    /**
     * 还款批次号
     */
    @ExcelProperty("还款批次号")
    @ColumnWidth(15)
    private String repayId;

    /**
     * 还款操作日
     */
    @ExcelProperty("还款操作日")
    @ColumnWidth(15)
    private LocalDateTime repayDate;

    /**
     * 起息日
     */
    @ExcelIgnore
    private LocalDate startRateDate;

    /**
     * 止息日
     */
    @ExcelIgnore
    private LocalDate endRateDate;

    /**
     * 汽服id
     */
    @ExcelIgnore
    private String ppid;

    @ExcelIgnore
    private String customerNo;

    /**
     * 服务生效时间
     */
    @ExcelIgnore
    private LocalDateTime serviceEffDate;

    /**
     * 服务失效时间
     */
    @ExcelIgnore
    private LocalDateTime serviceExpDate;

    /**
     * 生产商
     */
    @ExcelIgnore
    private String providerName;

    /**
     * 客户来源
     */
    @ExcelIgnore
    private String apiSource;

    /**
     * 客户来源payCompany
     */
    @ExcelIgnore
    private String source;

    /**
     * 代付通道
     */
    @ExcelIgnore
    private String payCompany;

    /**
     *  保司IP地址
     */
    @ExcelIgnore
    private String inCompanyIp;

    /**
     *  保司社会信用代码
     */
    @ExcelIgnore
    private String inCompanyCreditCode;

    @ExcelIgnore
    private String sunshineServiceCharge;

    /**
     * 来源渠道：1.语驾；2.阳光;
     */
    @ExcelIgnore
    private Integer channel;

    /**
     * 代理人编码
     */
    @ExcelIgnore
    private String agentCode;

    /**
     * 代理人名称
     */
    @ExcelIgnore
    private String agentName;

    /**
     * 主订单号
     */
    private String orderNo;

}
