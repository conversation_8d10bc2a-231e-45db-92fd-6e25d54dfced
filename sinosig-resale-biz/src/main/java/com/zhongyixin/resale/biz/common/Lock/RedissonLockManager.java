package com.zhongyixin.resale.biz.common.Lock;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;


/**
 * <AUTHOR>
 * @create 2024/3/20 17:41
 */
public class RedissonLockManager implements LockManager {

    private final DLockFactory<?> dLockFactory;

    public RedissonLockManager(DLockFactory<?> dLockFactory) {
        this.dLockFactory = dLockFactory;
    }

    @Override
    public DLock getLock(String key) {
        return dLockFactory.get(key);
    }
}
