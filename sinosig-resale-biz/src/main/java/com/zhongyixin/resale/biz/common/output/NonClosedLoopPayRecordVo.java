package com.zhongyixin.resale.biz.common.output;

import com.zhongyixin.resale.biz.common.sensitive.Sensitive;
import com.zhongyixin.resale.biz.common.sensitive.SensitiveStrategy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 非闭环支付记录
 * <AUTHOR>
 */
@Data
//@ApiModel("非闭环支付记录信息")
public class NonClosedLoopPayRecordVo {

//    @Schema(description = "第三方订单号", example = "11132133 ")
//    private String cardOrderNo;

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "姓名", example = "谢")
    private String userName;

    @Sensitive(strategy = SensitiveStrategy.PHONE)
    @Schema(description = "手机号", example = "***********")
    private String telephone;

    @Schema(description = "客户渠道", example = "GUOREN")
    private String apiSource;

    @Schema(description = "车牌号", example = "宁CKQ132")
    private String plateNum;

    @Schema(description = "批次密钥", example = "r4ov207t5ysaknhu6m")
    private String couponNo;

    @Schema(description = "银行卡号", example = "6216261000000000018")
    private String bankCardNo;

    @Schema(description = "支付金额", example = "123")
    private BigDecimal amount;

    @Schema(description = "创建时间", example = "2023-04-19 02:00:01")
    private LocalDateTime createTime;

    @Schema(description = "状态", example = "9")
    private Integer status;

    @Schema(description = "原因", example = "支付成功")
    private String transResult;

    @Schema(description = "支付机构", example = "拉卡拉")
    private String payCompany;

}
