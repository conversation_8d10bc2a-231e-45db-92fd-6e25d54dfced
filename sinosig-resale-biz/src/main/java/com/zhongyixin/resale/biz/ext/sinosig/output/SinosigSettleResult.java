package com.zhongyixin.resale.biz.ext.sinosig.output;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/3/6 15:17
 */
@Data
public class SinosigSettleResult {

    private static String successCode = "200";
    private static String successDesc = "OK";
    private static Boolean successResult = true;


    private String code;
    private String message;
    private Boolean result;

    public static SinosigSettleResult fail(String code, String message,boolean result){
        SinosigSettleResult sinosigSettleResult = new SinosigSettleResult();
        sinosigSettleResult.code = code;
        sinosigSettleResult.message = message;
        sinosigSettleResult.result = result;
        return sinosigSettleResult;
    }


    public static SinosigSettleResult ok(){
        SinosigSettleResult sinosigSettleResult = new SinosigSettleResult();
        sinosigSettleResult.code = successCode;
        sinosigSettleResult.message = successDesc;
        sinosigSettleResult.result = successResult;
        return sinosigSettleResult;
    }



}
