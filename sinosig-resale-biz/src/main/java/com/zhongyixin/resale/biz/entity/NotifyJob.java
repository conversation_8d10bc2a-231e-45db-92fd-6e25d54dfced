package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06 20:00:23
 */
@Data
@TableName("notify_job")
public class NotifyJob implements Serializable {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务订单号(唯一标识)
     */
    private String orderId;

    /**
     * 业务代码
     */
    private String businessCode;

    /**
     * 状态（1、待调度2、成功10、失败）
     */
    private Integer status;

    /**
     * 调用次数
     */
    private Integer count;

    /**
     * 参数
     */
    private String params;

    private Date nextTime;

    /**
     * 是否结束
     */
    private Integer ended;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除（0、否1、是）
     */
    private Integer deleted;


    private String remark;


}
