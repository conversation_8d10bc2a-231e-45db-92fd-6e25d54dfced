package com.zhongyixin.resale.biz.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.wftk.exception.core.exception.BusinessException;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Collection;


/**
 * excel响应工具类
 * <AUTHOR>
 */
public class ExcelUtil {

    /**
     * 设置excel下载响应头属性
     */
    public static void setExcelRespProp(HttpServletResponse response, String rawFileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setCharacterEncoding("UTF-8");
        String fileName = URLEncoder.encode(rawFileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.addHeader("Access-Control-Expose-Headers", "Content-disposition");
    }

    /**
     * 导出excel
     */
    public static void writer(HttpServletResponse response, Class<?> clazz, String sheetName, Collection<?> list) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .head(clazz)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet(sheetName)
                .doWrite(list);
    }


    /**
     * 自定义列宽导出excel
     */
    public static void adaptiveColumnWidthwriter(HttpServletResponse response, Class<?> clazz, String sheetName, Collection<?> list) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .head(clazz)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet(sheetName)
                .doWrite(list);
    }

    public static void verifyExcelFormat(MultipartFile file){
        if(file == null){
            throw new BusinessException("【上传文件为空】");
        }

        if (file.isEmpty() ) {
            throw new BusinessException("【上传文件为空】");
        }

        if(file.getSize() <= 0){
            throw new BusinessException("【上传文件内容为空】");
        }

        // 判断文件是xlsx结尾还是xls结尾
        String fileName  = file.getOriginalFilename();
        if(fileName == null){
            throw new BusinessException("【上传文件名称为空】");
        }
        if (!fileName.endsWith(CommonConstant.FILE_SUFFIX_XLSX)
                && !fileName.endsWith(CommonConstant.FILE_SUFFIX_XLS)) {
            throw new BusinessException("【上传excel文件格式错误】");
        }

        if (file.getSize() > CommonConstant.MAX_FILE_SIZE) {
            throw new BusinessException("【上传文件大小不能超过5M】");
        }
    }
}
