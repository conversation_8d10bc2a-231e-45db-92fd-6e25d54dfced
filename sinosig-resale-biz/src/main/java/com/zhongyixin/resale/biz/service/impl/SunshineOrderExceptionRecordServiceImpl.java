package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.SunshineOrderExceptionRecord;
import com.zhongyixin.resale.biz.mapper.SunshineOrderExceptionRecordMapper;
import com.zhongyixin.resale.biz.service.SunshineOrderExceptionRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
public class SunshineOrderExceptionRecordServiceImpl extends ServiceImpl<SunshineOrderExceptionRecordMapper, SunshineOrderExceptionRecord> implements SunshineOrderExceptionRecordService {

    /**
     * 新增
     * @param sunshineOrderExceptionRecord
     * @return
     */
    @Override
    public Boolean add(SunshineOrderExceptionRecord sunshineOrderExceptionRecord) {
        sunshineOrderExceptionRecord.setStatus(1);
        sunshineOrderExceptionRecord.setCreateTime(LocalDateTime.now());
        return save(sunshineOrderExceptionRecord);
    }
}
