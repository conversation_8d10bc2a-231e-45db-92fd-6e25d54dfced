package com.zhongyixin.resale.biz.common.input;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2023/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServiceOrderSearchDTO extends PaginationDTO{

    /**
     * 创建起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate createStartDate;

    /**
     * 创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate createEndDate;

    /**
     * 还款起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate repayStartDate;

    /**
     * 还款结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate repayEndDate;

    /**
     * 支付起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate tranStartDate;

    /**
     * 支付结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate tranEndDate;

    /**
     * 还款批次号
     */
    private String repayId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 父级机构代码
     */
    private String orgParentCode;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 还款状态
     */
    private Integer repayStatus;

    /**
     * 核销状态
     */
    private Integer status;

    /**
     * 代付商编
     */
    private String paymentNo;

    /**
     * 汽服主体服务商
     */
    private String providerName;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 订单模式
     */
    private Integer orderMode;

    /**
     * 支付渠道
     */
    private String payCompany;

}
