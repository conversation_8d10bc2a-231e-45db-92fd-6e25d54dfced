package com.zhongyixin.resale.biz.common.excel;

import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createDate 2023/10/24 10:54
 */
public class CellStyleWriteHandler implements CellWriteHandler {

    private List<String> originalValue;

    private Integer columnIndex;

    public CellStyleWriteHandler(List<String> originalValue, Integer columnIndex) {
        this.originalValue = originalValue;
        this.columnIndex = columnIndex;
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
            if(Objects.equals(context.getColumnIndex(), this.columnIndex)){
                if(originalValue.contains(context.getCell().getStringCellValue())){
                    // 内容的策略
                    WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
                    // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
                    contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                    // 背景红色
                    contentWriteCellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                    WriteCellData<?> cellData = context.getFirstCellData();
                    WriteCellStyle.merge(contentWriteCellStyle, cellData.getOrCreateStyle());
                }
            }
    }


}
