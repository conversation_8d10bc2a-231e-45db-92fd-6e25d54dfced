package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.common.input.QunqiuSinosigH5GenarateInput;
import com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO;
import com.zhongyixin.resale.biz.common.output.OrganizeInfoVO;
import com.zhongyixin.resale.biz.entity.OrganizeYgry;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public abstract class ServiceOrderConverter {

    public static ServiceOrderConverter INSTANCE = Mappers.getMapper(ServiceOrderConverter.class);

    public abstract OrganizeInfoVO entityToVo(OrganizeYgry organizeYgry);

    public abstract ServiceOrder copyDtoToEntity(ServiceOrderCopyDTO serviceOrderCopyDTO);

    public abstract List<ServiceOrder> copyDtoToEntity(List<ServiceOrderCopyDTO> serviceOrderCopyDTO);

    @Mapping(target = "card_code",source = "couponNo")
    public abstract QunqiuSinosigH5GenarateInput copyDtoToSinosigH5GenarateInput(ServiceOrderCopyDTO serviceOrderCopyDTO);
}
