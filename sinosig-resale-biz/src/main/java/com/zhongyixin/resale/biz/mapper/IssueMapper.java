package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.Issue;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface IssueMapper extends BaseMapper<Issue> {

    /**
     * 根据客户来源查询发布中的发布详情
     *
     * @param apiSource
     * @param orderType
     * @return
     */
    Issue selectOneByApiSource(@Param("apiSource") String apiSource,@Param("orderType") String orderType);


    List<Issue> selectListByAutoAddDay();


}
