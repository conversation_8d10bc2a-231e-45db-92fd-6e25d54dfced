package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 * @create 2023/2/3 16:43
 */
public interface YuJiaConstant {

    /**
     * 客户来源
     */
    String API_SOURCE = "apiSource";

    /**
     * redis标点符号
     */
    String COLON = ":";

    /**
     * 语驾通知订单状态：成功
     */
    String SUCCESS_SERVICE_STATUS = "1";

    /**
     * 省
     */
    String PROVINCE = "省";

    /**
     * 市
     */
    String CITY = "市";

    /**
     * 自治区
     */
    String MUNICIPALITY = "自治区";



    interface Client {

        /**
         * 平安
         */
        String PING_AN = "ping_an";


        /**
         * 国任
         */
        String GUO_REN = "guo_ren";


        /**
         * 渠道
         */
        String CHANNEL = "channel";

        /**
         * 阳光
         */
        String SUNSHINE = "sunshine";
    }



    /**
     * API相关
     */
    interface API {

        /**
         * 平安
         */
        interface PingAn {

            /**
             * 查询积分
             */
            String QUERY_POINT = "/querycardbyname";

            /**
             * 核销回调
             */
            String PAY_SUCCESS_WRITE_OFF = "/pabxupdate";

        }

        /**
         * 国任
         */
        interface GuoRen {

            /**
             * 查询积分
             */
            String QUERY_POINT = "/vipbalance/query";

            /**
             * 积分锁定
             */
            String LOCK_POINT = "/vipbalance/writeoff";

            /**
             * 成功核销回调
             */
            String PAY_SUCCESS_WRITE_OFF = "/vipbalance/succeed";

            /**
             * 支付失败回退积分
             */
            String PAY_FAIL_RETURN_POINT = "/vipbalance/returnorder";
        }

        /**
         * 渠道
         */
        interface Channel {

            /**
             * 查询积分
             */
            String QUERY_POINT = "/placeuser/querycard";

            /**
             * 核销回调
             */
            String PAY_SUCCESS_WRITE_OFF = "/placeuser/writeoff";
        }

        /**
         * 阳光
         */
        interface Sunshine{
            /**
             * 查询积分
             */
            String QUERY_POINT = "/queryyjrycard";

            /**
             * 核销回调
             */
            String PAY_SUCCESS_WRITE_OFF = "/ygryupdate";
        }
    }

}
