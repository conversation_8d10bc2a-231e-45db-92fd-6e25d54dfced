package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 * @create 2023/2/10 16:14
 */
public interface OrderConstant {

    /**
     * 订单状态：初始化
     */
    String INIT_ORDER_STATUS = "0";

    /**
     * 订单状态：重复订单
     */
    String REPETITION_ORDER_STATUS = "-1";

    /**
     * 订单状态：异常订单（险司额度不足）
     */
    String EXCEPTION_ORDER_STATUS = "10";

    /**
     * 订单状态：处理中
     */
    String DEALING_ORDER_STATUS = "1";

    /**
     * 订单状态：支付成功
     */
    String SUCCESS_ORDER_STATUS = "2";

    /**
     * 订单状态：支付失败
     */
    String FAIL_ORDER_STATUS = "9";

    /**
     * 订单状态：订单取消
     */
    String CANCLE_ORDER_STATUS = "7";

    /**
     * 投放方式
     */
    interface PayWay {
        String ALIPAY = "支付宝";
        String BANK_CARDS = "银行卡";
    }


    /**
     * 订单类型
     */
    interface OrderType{
        /**
         * 新阳光闭环
         */
        String NEW_YGBH = "new_ygbh";

        /**
         * 阳光闭环
         */
        String YGBH = "ygbh";

        /**
         * 新国任
         */
        String NEW_GUOREN = "new_guoren";

        /**
         * 新国任
         */
        String NEW_PINGAN = "new_chepai";
    }

    interface ApiSource{
        /**
         * 国任
         */
        String GUOREN = "GUOREN";
    }
}
