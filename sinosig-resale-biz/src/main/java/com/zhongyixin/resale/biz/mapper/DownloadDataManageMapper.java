package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.DownloadDataManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 下载数据管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface DownloadDataManageMapper extends BaseMapper<DownloadDataManage> {

    DownloadDataManage selectOneByBatchNo(@Param("batchNo")String batchNo);

    /**
     * 分页
     * @param startDate
     * @param endDate
     * @return
     */
    List<DownloadDataManage> pageList(@Param("startDate")String startDate,
                              @Param("endDate")String endDate,
                                      @Param("createBy")String createBy);

    Integer updateDownloadDataManageByBatchNo(DownloadDataManage downloadDataManage);

    /**
     * 不写过不了权限过滤
     * @return
     */
    Integer pageList_COUNT();

}
