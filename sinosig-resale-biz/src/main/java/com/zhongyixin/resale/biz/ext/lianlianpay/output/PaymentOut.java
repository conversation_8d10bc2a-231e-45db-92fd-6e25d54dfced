package com.zhongyixin.resale.biz.ext.lianlianpay.output;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PaymentOut {

    private static final String PAY_SUCCESS = "0000";

    /** 请求结果代码 . */
    private String ret_code;

    /** 请求结果描述 . */
    private String ret_msg;

    /** 原请求中商户订单号 . */
    private String no_order;

    /** 商户编号 . */
    private String oid_partner;

    /** 连连支付单号 . */
    private String oid_paybill;

    /** 确认码. */
    private String confirm_code;

    /** 签名方式 . */
    private String sign_type;

    /** 签名方 . */
    private String sign;


    /**
     * UNP00000-支付成功
     * UNP12345-受理成功，后继需要等待通知或查询结果
     * @return
     */
    public boolean successed() {
        return PAY_SUCCESS.equalsIgnoreCase(ret_code);
    }


}
