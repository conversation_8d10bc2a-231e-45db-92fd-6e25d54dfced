package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.SystemSetting;
import com.zhongyixin.resale.biz.mapper.SystemSettingMapper;
import com.zhongyixin.resale.biz.service.SystemSettingService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Service
public class SystemSettingServiceImpl extends ServiceImpl<SystemSettingMapper, SystemSetting> implements SystemSettingService {

    @Resource
    SystemSettingMapper systemSettingMapper;

    @Override
    public SystemSetting selectOneByItemName(String itemName) {
        return systemSettingMapper.selectOneByItemName(itemName);
    }

}
