package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.entity.UserOperateInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2023/2/9 17:40
 */
@Mapper
public abstract class CommonDtoToVoConvert {

    public static CommonDtoToVoConvert INSTANCE = Mappers.getMapper(CommonDtoToVoConvert.class);

    /**
     * 订单转用户操作类
     * @param order 订单
     * @return 用户操作类
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    public abstract UserOperateInfo orderToUserOperateInfo(Order order);

    /**
     * 订单转用户操作类
     * @param order 订单
     * @return 用户操作类
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    public abstract Order orderToOrder(Order order);

//    /**
//     * 订单转民生订单
//     * @param order 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "id",ignore = true)
//    @Mapping(target = "status",ignore = true)
//    @Mapping(target = "createTime",ignore = true)
//    @Mapping(source = "telphone",target = "telephone")
//    public abstract PayOrderCmbc orderToPayOrderCmbc(Order order);

    /**
     * 订单转连连订单
     * @param order 订单
     * @return 连连订单
     */
    @Mapping(target = "id",ignore = true)
    @Mapping(target = "status",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    @Mapping(source = "telphone",target = "mobileNumber")
    @Mapping(source = "userName",target = "cardName")
    @Mapping(source = "bankCardNo",target = "cardNo")
    @Mapping(source = "amount",target = "moneyOrder")
    public abstract PayOrderLianlian orderToPayOrderLianlian(Order order);

//    /**
//     * 订单转拉卡拉订单
//     * @param order 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "id",ignore = true)
//    @Mapping(target = "status",ignore = true)
//    @Mapping(target = "createTime",ignore = true)
//    @Mapping(source = "userName",target = "cardName")
//    @Mapping(source = "telphone",target = "mobileNumber")
//    @Mapping(source = "bankCardNo",target = "cardNo")
//    @Mapping(source = "amount",target = "actAmount")
//    public abstract PayOrderLakala orderToPayOrderLakala(Order order);
//
//
//    /**
//     * 订单转银商订单
//     * @param order 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "id",ignore = true)
//    @Mapping(target = "status",ignore = true)
//    @Mapping(target = "createTime",ignore = true)
//    @Mapping(target = "tranDate",ignore = true)
//    @Mapping(source = "telphone",target = "telephone")
//    @Mapping(source = "amount",target = "actAmount")
//    public abstract PayOrderYinshang orderToPayOrderYinshang(Order order);
//
//    /**
//     * 订单转新拉卡拉订单
//     * @param order 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "id",ignore = true)
//    @Mapping(target = "status",ignore = true)
//    @Mapping(target = "createTime",ignore = true)
//    @Mapping(source = "telphone",target = "telephone")
//    public abstract PayOrderNewLakala orderToPayOrderNewLakala(Order order);
//
//    /**
//     * 民生代付出参转民生订单
//     * @param payT005Output 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "amount" ,ignore = true)
//    @Mapping(source = "merchantSeq" , target = "payOrderId")
//    public abstract PayOrderCmbc payT005OutputToPayOrderCmbc(PayT005Output payT005Output);
//
//    /**
//     * 民生查询出参转民生订单
//     * @param payQ001Output 订单
//     * @return 民生订单
//     */
//    @Mapping(target = "amount" ,ignore = true)
//    @Mapping(target = "status" ,ignore = true)
//    public abstract PayOrderCmbc payQ001OutputToPayOrderCmbc(PayQ001Output payQ001Output);



}
