package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.Protocol;
import com.zhongyixin.resale.biz.mapper.ProtocolMapper;
import com.zhongyixin.resale.biz.service.ProtocolService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户协议表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class ProtocolServiceImpl extends ServiceImpl<ProtocolMapper, Protocol> implements ProtocolService {

    @Resource
    ProtocolMapper protocolMapper;

    /**
     * 获取协议内容
     * @param protocol protocol
     * @return html内容
     */
    @Override
    public String getHTMLContent(Protocol protocol) {
        return protocolMapper.getHTMLContent(protocol);
    }
}
