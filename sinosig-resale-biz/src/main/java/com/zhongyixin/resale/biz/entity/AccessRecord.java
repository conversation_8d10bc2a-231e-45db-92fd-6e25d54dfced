package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户访问记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_access_record")
public class AccessRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 来源
     */
    private String apiSource;

    /**
     * 类型
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * 设备
     */
    private String device;

    /**
     * 所在地
     */
    private String location;

    /**
     * 标识
     */
    private String identify;

    /**
     * 参数
     */
    private String param;

    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 状态（0.正常访问 1.访问失败）
     */
    private Integer status;

}
