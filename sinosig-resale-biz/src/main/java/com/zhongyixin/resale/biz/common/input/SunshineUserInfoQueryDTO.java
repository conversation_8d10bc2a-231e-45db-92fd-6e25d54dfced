package com.zhongyixin.resale.biz.common.input;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR>
 * @createDate 2024/3/12 13:44
 */
@Data
public class SunshineUserInfoQueryDTO {

    /**
     * 用户手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String telephone;

    /**
     * 卡密
     */
    @NotBlank(message = "卡密不能为空")
    private String cardCode;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    /**
     * 重定向路径
     */
    private String redirectUrl;

    /**
     * 客户来源
     */
    @NotBlank(message = "客户来源不能为空")
    private String apiSource;

}
