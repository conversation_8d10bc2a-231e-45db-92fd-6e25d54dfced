package com.zhongyixin.resale.biz.common.output;

import com.zhongyixin.resale.biz.common.sensitive.Sensitive;
import com.zhongyixin.resale.biz.common.sensitive.SensitiveStrategy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SunshinePayRecordVo {

    private Integer id;

    private String orderId;

    private String userName;

    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String telephone;

    private String bankCardNo;

    private BigDecimal amount;

    private LocalDateTime createTime;

    private Integer status;

    private String transResult;

    private String payCompany;
}
