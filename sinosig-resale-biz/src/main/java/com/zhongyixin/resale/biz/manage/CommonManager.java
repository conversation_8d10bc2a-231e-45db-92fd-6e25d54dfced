package com.zhongyixin.resale.biz.manage;

import cn.hutool.core.util.StrUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.ChannelSettingConstant;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.entity.ChannelSetting;
import com.zhongyixin.resale.biz.entity.Issue;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <p>
 * 公共服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@Slf4j
public class CommonManager {

    @Resource
    NotifyJobService notifyJobService;

    @Autowired
    private LockManager lockManager;

    @Resource
    ChannelSettingService channelSettingService;


    /**
     * 验签以及转换参数
     *
     * @param jsonStr
     * @return
     */
//    public ResultBean<BaseUserInfoVO> verifySignAndConvertParams(String jsonStr) {
//        JSONObject jsonObject = JSONObject.getInstance();
//        Map<String, String> params = jsonObject.parseMap(jsonStr, String.class, String.class);
//        // 记录用户访问详情
//        AccessRecord accessRecord = new AccessRecord();
//        try {
//            //url 参数
//            String param = params.get("params");
//            String type = params.get("type");
//            log.info("验签 type：{} params：{}", type, param);
//            accessRecord.setType(type);
//            byte[] decode = Base64.getDecoder().decode(param.replaceAll(" +", "+"));
//
//            String jsonString = new String(decode);
//            accessRecord.setParam(jsonString);
//            Map<String, Object> paramsMap = jsonObject.parseMap(jsonString, String.class, Object.class);
//            String apiSource = "";
//            Object apiSourceObj = paramsMap.get("apiSource");
//            if (apiSourceObj != null) {
//                apiSource = apiSourceObj.toString();
//                if (apiSource.equals(yuJiaApiManager.getSunshineManager().getSunshineApiSource())) {
//                    paramsMap = paramsMap.entrySet().stream().filter((e) -> e.getValue() != null && e.getValue() != "").collect(Collectors.toMap(
//                            Map.Entry::getKey,
//                            Map.Entry::getValue));
//                }
//            }
//
//            accessRecord.setApiSource(apiSource);
//            accessRecord.setIdentify(getIdentify(paramsMap));
//
//            Boolean verifySign = yuJiaApiManager.getBusinessManager().verifySign(paramsMap);
//            if (!verifySign) {
//                accessRecord.setStatus(CommonConstant.AccessRecord.STATUS_FAIL);
//                accessRecord.setErrorInfo("非法签名");
//                return ResultBean.fail("非法签名");
//            }
//
//            paramsMap.put("orderType", type);
//            BaseUserInfoVO baseUserInfoVO = yuJiaApiManager.getBusinessManager().convertParams(paramsMap);
//
//            return ResultBean.success(baseUserInfoVO);
//        } catch (Exception e) {
//            accessRecord.setStatus(CommonConstant.AccessRecord.STATUS_FAIL);
//            accessRecord.setErrorInfo(e.getMessage());
//            throw e;
//        } finally {
//            accessRecordService.addAccessRecord(accessRecord);
//        }
//    }



    public Integer saveNotifyJob(String businessCode, String orderId, Object param) {
        // 加锁
        String key =  businessCode + orderId;
        String paramJsonStr = JSONObject.getInstance().toJSONString(param);
        DLock lock = lockManager.getCreateNotifyJobLock(key);
        try {
            if (lock.tryLock()) {
                return notifyJobService.saveNotifyJob(businessCode, orderId, paramJsonStr);
            }else{
                log.warn("save notifyJob wait timeout. businessCode: {}, orderId: {}, param: {}",
                        businessCode, orderId, paramJsonStr);
                throw new BusinessException("save notifyJob wait timeout");
            }
        } finally {
            lock.unLock();
        }
    }




    public Issue queryIssue(String apiSource) {
        if (StrUtil.isBlank(apiSource)) {
            log.error(apiSource + "渠道查询发布详情异常，apiSource为空");
            Issue issue = new Issue();
            issue.setStatus(CommonConstant.Issue.OFF);
            return (issue);
        }

        // 通道维护
        ChannelSetting channelSetting = channelSettingService.selectOneByChannelCode(apiSource);
        if (channelSetting != null) {
            if (Objects.equals(channelSetting.getStatus(), ChannelSettingConstant.Status.MAINTENANCE)) {
                // 维护中
                Issue issue = new Issue();
                issue.setStatus(CommonConstant.Issue.ON);
                issue.setContent(channelSetting.getRemark());
                return issue;
            }
        }

        Issue issue = new Issue();
        issue.setStatus(CommonConstant.Issue.OFF);
        return (issue);
    }



}
