package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.constant.NotifyJobConstant;
import com.zhongyixin.resale.biz.common.enums.ScheduledEnum;
import com.zhongyixin.resale.biz.entity.NotifyJob;
import com.zhongyixin.resale.biz.mapper.NotifyJobMapper;
import com.zhongyixin.resale.biz.service.NotifyJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06 20:00:23
 */
@Service
@Slf4j
public class NotifyJobServiceImpl extends ServiceImpl<NotifyJobMapper, NotifyJob> implements NotifyJobService {

    @Override
    public List<NotifyJob> queryWaitScheduled() {
        return baseMapper.queryWaitScheduled();
    }

    @Override
    public Integer updateNotifyJob(NotifyJob job) {
        return baseMapper.updateNotifyJob(job);
    }

    @Override
    public NotifyJob selectOneByBusinessCodeAndOrderId(String businessCode, String orderId) {
        return baseMapper.selectOneByBusinessCodeAndOrderId(businessCode,orderId);
    }

    @Transactional
    @Override
    public Integer saveNotifyJob(String businessCode, String orderId, String paramsJson) {
        NotifyJob notifyJob = selectOneByBusinessCodeAndOrderId(businessCode,
                orderId);

        if(notifyJob == null){
            notifyJob = new NotifyJob();
            notifyJob.setOrderId(orderId);
            notifyJob.setBusinessCode(businessCode);
            notifyJob.setCount(0);
            notifyJob.setParams(paramsJson);
            notifyJob.setNextTime(DateUtil.offsetSecond(new Date(), ScheduledEnum.def.getSecond()));
            notifyJob.setStatus(NotifyJobConstant.Status.WAIT);
            notifyJob.setEnded(NotifyJobConstant.Ended.NO);

            notifyJob.setCreateTime(LocalDateTime.now());
            notifyJob.setUpdateTime(LocalDateTime.now());
            notifyJob.setDeleted(0);
            save(notifyJob);

            return notifyJob.getId();
        }else {
            log.info("businessCode:{}, orderId:{}, 该notifyJob任务已存在",businessCode, orderId);
        }

        return 0;
    }

    @Override
    public NotifyJob queryJobByCodeAndOrderId(String code, String orderId) {
        return baseMapper.queryJobByCodeAndOrderId(code,orderId);
    }

}
