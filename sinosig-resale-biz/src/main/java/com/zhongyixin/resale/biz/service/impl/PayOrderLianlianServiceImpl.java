package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.mapper.PayOrderLianlianMapper;
import com.zhongyixin.resale.biz.service.PayOrderLianlianService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PayOrderLianlianServiceImpl extends ServiceImpl<PayOrderLianlianMapper, PayOrderLianlian> implements PayOrderLianlianService {

    @Resource
    PayOrderLianlianMapper payOrderLianlianMapper;

    /**
     * 根据支付Id查询民生订单
     * @param payOrderId
     * @return
     */
    @Override
    public PayOrderLianlian selectPayOrderLianlianByPayOrderId(String payOrderId) {
        return payOrderLianlianMapper.selectPayOrderLianlianByPayOrderId(payOrderId);
    }

    /**
     * 根据支付Id修改民生订单信息
     * @param payOrderLianlian
     * @return
     */
    @Override
    public Integer updatePayOrderLianlianByPayOrderId(PayOrderLianlian payOrderLianlian) {
        return payOrderLianlianMapper.updatePayOrderLianlianByPayOrderId(payOrderLianlian);
    }

    /**
     * 查询10分钟前的订单
     * @return
     */
    @Override
    public List<PayOrderLianlian> selectOrderByTenMinutes() {
        return payOrderLianlianMapper.selectOrderByTenMinutes();
    }

    @Override
    public PayOrderLianlian findUnfinishedOrder(String orderId) {
        return payOrderLianlianMapper.selectUnfinishedOrderByOrderId(orderId);
    }

    @Override
    public PayOrderLianlian findPaidOrPayingOrder(String orderId) {
        return payOrderLianlianMapper.selectPayingOrPaidOrderByOrderId(orderId);
    }
}
