package com.zhongyixin.resale.biz.common.output;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.zhongyixin.resale.biz.common.excel.converter.LocalDateConverter;
import com.zhongyixin.resale.biz.common.sensitive.Sensitive;
import com.zhongyixin.resale.biz.common.sensitive.SensitiveStrategy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2023/9/19 15:09
 */
@Data
public class SunshineRepaymentOrderVO {

    /**
     * 还款批次号
     */
    @ExcelProperty("还款批次号")
    @ColumnWidth(15)
    private String repaymentId;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    @ColumnWidth(15)
    private String orderId;


    /**
     * 金额
     */
    @ExcelProperty("金额")
    @ColumnWidth(15)
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    @ExcelProperty("用户姓名")
    @ColumnWidth(15)
    @Sensitive(strategy = SensitiveStrategy.USERNAME)
    private String userName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @ColumnWidth(15)
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String telephone;

    /**
     * 支付成功时间
     */
    @ExcelProperty("支付成功时间")
    @ColumnWidth(15)
    private LocalDateTime tranDate;

    /**
     * 父级机构代码
     */
    @ExcelProperty("二级机构代码")
    @ColumnWidth(15)
    private String orgParentCode;

    /**
     * 机构代码
     */
    @ExcelProperty("三级机构代码")
    @ColumnWidth(15)
    private String orgCode;

    /**
     * 服务商
     */
    @ExcelProperty("服务商")
    @ColumnWidth(15)
    private String providerName;

    /**
     * 止息日
     */
    @ExcelProperty(value = "止息日",converter = LocalDateConverter.class)
    @ColumnWidth(15)
    private LocalDate endRateDate;

    /**
     * 垫资天数
     */
    @ExcelProperty("垫资天数")
    @ColumnWidth(15)
    private Integer advanceFundDay;

    /**
     * 利息
     */
    @ExcelProperty(value = "利息")
    @ColumnWidth(15)
    private BigDecimal interest;

}
