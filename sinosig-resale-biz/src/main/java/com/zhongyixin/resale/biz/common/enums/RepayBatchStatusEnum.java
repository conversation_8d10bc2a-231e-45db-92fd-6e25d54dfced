package com.zhongyixin.resale.biz.common.enums;

/**
 * <AUTHOR>
 */
public enum RepayBatchStatusEnum {

    //状态码
    INIT(0, "初始化"),
    UPLOAD_FAIL(8, "上传失败"),
    UPLOAD_SUCCESS(1, "上传成功"),
    FILE_ASSOCIATIONING(2, "文件关联中"),
    FILE_ASSOCIATION_FAIL(9, "文件关联失败"),
    FILE_ASSOCIATION_SUCCESS(3, "文件关联成功");



    public final Integer status;

    public final String msg;


    RepayBatchStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMsg() {
        return msg;
    }


}
