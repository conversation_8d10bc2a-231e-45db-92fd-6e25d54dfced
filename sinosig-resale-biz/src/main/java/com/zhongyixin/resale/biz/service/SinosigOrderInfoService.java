package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.entity.SinosigOrderInfo;

import java.time.LocalDate;

/**
 * <p>
 * 阳光订单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface SinosigOrderInfoService extends IService<SinosigOrderInfo> {

    void createSinosigOrderInfo(ServiceOrder serviceOrder, String orderId);

    void updateRepayInfoByRepayBatchNo(String repayBatchNo, String repayId, LocalDate endRateDate);


    Integer updateStartRateInfoBySinosigOrderId(String sinosigOrderId,LocalDate startRateDate);



}
