package com.zhongyixin.resale.biz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/6 14:31
 */
@AllArgsConstructor
@Getter
public enum ErrorCodeEnum {

    /**
     * 错误码枚举
     */
    SYSTEM_ERROR("0001","系统繁忙，请稍后重试"),
    SEND_INFO_BLANK("0001","sendInfo为空"),
    PARAM_DECRYPT_ERROR("0001","参数解密错误"),
    PARAM_INVALID("0001","参数格式无效"),
    SIGN_BLANK("0001","sign为空"),
    SIGN_VERIFY_FAIL("0001","签名验证失败"),

    ORDER_NOT_EXIST("0001","订单不存在"),

    ORDER_ID_BLANK("0001","订单号为空"),

    MODIFY_TO_NAME_BLANK("0001","客户新的姓名为空"),

    MODIFY_TO_ID_NO_BLANK("0001","客户新的身份证号为空"),

    NOT_CANCEL("0001","当前订单已发生业务不能撤销"),
    NOT_UPDATE("0001","当前订单领取中或已领取不能修改");




    private final String code;

    private final String message;

}
