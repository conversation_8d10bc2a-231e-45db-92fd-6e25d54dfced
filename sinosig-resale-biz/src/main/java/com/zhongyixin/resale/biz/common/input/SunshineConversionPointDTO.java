package com.zhongyixin.resale.biz.common.input;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR>
 * @create 2023/2/9 11:02
 */
@Data
public class SunshineConversionPointDTO {

    /**
     * 用户手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String telephone;

    /**
     * 卡密
     */
    @NotBlank(message = "卡密不能为空")
    private String card_code;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    /**
     * 金额
     */
    @NotBlank(message = "金额不能为空")
    private String balance;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankCardNo;

    /**
     * 重定向路径
     */
    private String redirectUrl;

    /**
     * 路径跳转类型 (订单类型)
     */
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    /**
     * 客户来源
     */
    @NotBlank(message = "客户来源不能为空")
    private String apiSource;

    /**
     * 客户来源
     */
    @NotBlank(message = "客户来源不能为空")
    private String source;
}
