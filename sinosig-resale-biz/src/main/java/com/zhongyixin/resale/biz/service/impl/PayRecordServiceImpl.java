package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.input.NonClosedLoopPayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.input.SunshinePayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.output.NonClosedLoopPayRecordVo;
import com.zhongyixin.resale.biz.common.output.SunshinePayRecordVo;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayRecord;
import com.zhongyixin.resale.biz.mapper.PayRecordMapper;
import com.zhongyixin.resale.biz.service.PayRecordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 支付记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
public class PayRecordServiceImpl extends ServiceImpl<PayRecordMapper, PayRecord> implements PayRecordService {

    @Resource
    PayRecordMapper payRecordMapper;


    /**
     * @param order
     * @param status
     * @param payOrderId
     * @param payCompany
     * @param tranResult
     * @return
     */
    @Override
    public Boolean insertPayRecord(Order order, int status, String payOrderId, String payCompany, String tranResult) {
        PayRecord payRecord = new PayRecord();
        payRecord.setBankCardNo(order.getBankCardNo());
        payRecord.setAmount(order.getAmount());
        payRecord.setApiSource(order.getApiSource());
        payRecord.setCreateTime(LocalDateTime.now());
        payRecord.setOrderId(order.getOrderId());
        payRecord.setPayCompany(payCompany);
        payRecord.setStatus(status);
        payRecord.setPaymentNo(payOrderId);
        payRecord.setTelephone(order.getTelphone());
        payRecord.setTransResult(tranResult);
        return save(payRecord);
    }

    @Override
    public PayRecord selectLastTimeOne(String orderId, Integer status) {
        return payRecordMapper.selectLastTimeOne(orderId,status);
    }
}
