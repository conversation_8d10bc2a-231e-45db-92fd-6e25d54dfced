package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.SysMenu;

import java.util.List;

/**
 * <p>
 * 菜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 根据用户ID获取动态路由
     * @param userId
     * @return
     */
    List<SysMenu> selectAuthMenuByUserId(Integer userId);

    /**
     * 获取超管动态路由
     * @return
     */
    List<SysMenu> selectAuthMenuByAdmin();
}
