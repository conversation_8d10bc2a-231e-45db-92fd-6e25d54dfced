package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO;
import com.zhongyixin.resale.biz.common.input.SinosigOrderCopyDTO;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.entity.SinosigOrderInfo;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderReceiveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 18:26
 */
@Mapper
public abstract class SinosigDtoConvert {

    public static SinosigDtoConvert INSTANCE = Mappers.getMapper(SinosigDtoConvert.class);

    /**
     * 阳光通知参数转换为阳光订单
     * @param sinosigOrderReceiveDTO 阳光通知参数
     * @return 阳光订单
     */
    @Mapping(source="phone",target = "telephone")
    @Mapping(source="name",target = "userName")
    @Mapping(source="ppdid",target = "ppid")
    public abstract ServiceOrder sinosigOrderReceiveDTOToServiceOrder(SinosigOrderReceiveDTO sinosigOrderReceiveDTO);


    /**
     * ServiceOrderCopyDTO转换为阳光订单
     * @param copyDTO
     * @return
     */
    public abstract ServiceOrder toServiceOrder(ServiceOrderCopyDTO copyDTO);

    /**
     * ServiceOrderCopyDTO转换为阳光订单
     * @param copyDTO
     * @return
     */
    public abstract ServiceOrder copyDTOToServiceOrder(SinosigOrderCopyDTO copyDTO);


    /**
     * 阳光订单转换为业务订单
     * @param serviceOrder 阳光订单参数
     * @return 订单
     */
    @Mapping(source="orderId",target = "cardOrderNo")
    @Mapping(source="amount",target = "point")
    @Mapping(source="telephone",target = "telphone")
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    public abstract Order serviceOrderToOrder(ServiceOrder serviceOrder);


    /**
     * ServiceOrder转换为SinosigOrderInfo订单
     * @param serviceOrder
     * @return
     */
    @Mapping(source="orderId",target = "sinosigOrderId")
    @Mapping(target = "orderId", ignore = true)
    @Mapping(target = "id", ignore = true)
    public abstract SinosigOrderInfo toSinosigOrderInfo(ServiceOrder serviceOrder);
}
