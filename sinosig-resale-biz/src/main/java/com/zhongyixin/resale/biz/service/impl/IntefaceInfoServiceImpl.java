package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.IntefaceInfo;
import com.zhongyixin.resale.biz.mapper.IntefaceInfoMapper;
import com.zhongyixin.resale.biz.service.IntefaceInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 请求接口信息 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class IntefaceInfoServiceImpl extends ServiceImpl<IntefaceInfoMapper, IntefaceInfo> implements IntefaceInfoService {

    @Resource
    IntefaceInfoMapper intefaceInfoMapper;

    /**
     * 补齐响应信息
     * @param intefaceInfo 请求id
     * @return
     */
    @Override
    public Integer updateIntefaceInfoByRequestId(IntefaceInfo intefaceInfo) {
        return intefaceInfoMapper.updateIntefaceInfoByRequestId(intefaceInfo);
    }
}
