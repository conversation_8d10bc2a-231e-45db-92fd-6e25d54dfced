package com.zhongyixin.resale.biz.common.util;

import cn.hutool.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取地址类
 * 
 * <AUTHOR>
 */
public class AddressUtils
{
    private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

    /**
     * IP地址查询
     */
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";

    /**
     * 未知地址
     */
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIp(String ip)
    {
        // 内网不查询
        if (IpUtils.internalIp(ip))
        {
            return "内网IP";
        }
            try
            {
                String rspStr = HttpUtil.get(IP_URL+"?ip=" + ip + "&json=true");
                if (StringUtils.isEmpty(rspStr))
                {
                    log.error("获取地理位置异常 {}", ip);
                    return UNKNOWN;
                }
                return rspStr;
            }
            catch (Exception e)
            {
                log.error("获取地理位置异常 {}", ip);
            }
        return UNKNOWN;
    }

    public static void main(String[] args) {
        AddressUtils.getRealAddressByIp("**************");
    }


}
