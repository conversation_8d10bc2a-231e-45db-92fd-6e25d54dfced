package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.common.annotation.DataPermission;
import com.zhongyixin.resale.biz.common.annotation.DataScopeType;
import com.zhongyixin.resale.biz.common.enums.DataScope;
import com.zhongyixin.resale.biz.entity.SysDictData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface SysDictDataMapper extends BaseMapper<SysDictData> {

    /**
     * 根据字典类型查询字典数据(汽服主体)
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "dict_value")
    })
    List<SysDictData> selectCarCompanyDictDataByType(String dictType);

    /**
     * 根据字典类型查询字典数据
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<SysDictData> selectDictDataByType(String dictType);

    /**
     * 根据字典类型查询字典数据
     * @param dictType 字典类型
     * @param dictLabel 字典标签
     * @return 字典数据集合信息
     */
    SysDictData selectDictDataByTypeAndLabel(@Param("dictType") String dictType,@Param("dictLabel") String dictLabel);

}
