package com.zhongyixin.resale.biz.ext.sinosig.enums;


import lombok.Getter;

@Getter
public enum WriteOffWay {

    //实时返回码
    BANK("0", "银行卡"),
    ALIPAY("1","支付宝");


    public final String code;

    public final String label;


    WriteOffWay(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static WriteOffWay getRetCodeEnumByLabel(String label) {
        for (WriteOffWay way : WriteOffWay.values()) {
            if(way.getLabel().equals(label)){
                return way;
            }

        }
        return null;
    }
}
