package com.zhongyixin.resale.biz.common.Lock;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;

/**
 * <AUTHOR>
 * @create 2024/3/20 17:29
 */
public interface LockManager {


    /**
     * 获取锁
     * @param key
     * @return
     */
    DLock getLock(String key);

    /**
     * 获取创建任务锁
     * @param notifyJob<PERSON><PERSON>
     * @return
     */
    default DLock getCreateNotifyJobLock(String notifyJob<PERSON>ey) {
        return getLock("LOCK:NOTIFYJOB:CREATE:" + notify<PERSON><PERSON><PERSON><PERSON>);
    }


}
