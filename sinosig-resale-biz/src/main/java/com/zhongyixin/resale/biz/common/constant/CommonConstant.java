package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 * @create 2023/2/17 11:12
 */
public interface CommonConstant {

    /**
     * 验证码
     */
    String CODE_KEY  = "captcha:";

    /**
     * 令牌前缀
     */
    String LOGIN_USER_KEY = "login_user_key";

    /**
     * 登录用户 redis key
     */
    String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 时间相差最大天数
     */
    Integer DATE_DIFF_MAX_DAY = 180;

    /**
     * 最大文件大小
     */
    Integer MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 文件后缀 xlsx
     */
    String FILE_SUFFIX_XLSX = ".xlsx";

    /**
     * 文件后缀 xls
     */
    String FILE_SUFFIX_XLS = ".xls";

    interface SysUser{

        /**
         * 用户逻辑删除：已删除
         */
        String DELETED = "1";

        /**
         * 用户逻辑删除：未删除
         */
        String UNDELETED = "0";

        /**
         * 用户逻辑删除：已下架
         */
        String PUTED = "1";

        /**
         * 用户逻辑删除：未删除
         */
        String UNPUTED = "0";


    }

    interface ServiceOrder{

        /**
         * 垫资状态：未垫资
         */
        String REPAYSTATUS_UNUSED = "0";

        /**
         * 垫资状态：处理中
         */
        String REPAYSTATUS_PROCESSED = "1";

        /**
         * 垫资状态：已垫资
         */
        String REPAYSTATUS_USED = "2";

        /**
         * 垫资状态：已还款
         */
        String REPAYSTATUS_REPAYED = "3";



        /**
         * 垫资状态：未垫资
         */
        String REPAYSTATUS_STRING_UNUSED = "未支付";

        /**
         * 垫资状态：处理中
         */
        String REPAYSTATUS_STRING_PROCESSED = "处理中";
        /**
         * 垫资状态：已垫资
         */
        String REPAYSTATUS_STRING_USED = "已支付";

        /**
         * 垫资状态：已还款
         */
        String REPAYSTATUS_STRING_REPAYED = "已还款";

    }


    interface OrderAdmin{

        /**
         * 垫资状态：未支付
         */
        String REPAYSTATUS_UNUSED = "0";

        /**
         * 垫资状态：支付中
         */
        String REPAYSTATUS_USE = "1";

        /**
         * 垫资状态：支付成功
         */
        String REPAYSTATUS_SUCCESS = "2";

        /**
         * 垫资状态：支付失败
         */
        String REPAYSTATUS_FAIL = "9";



        /**
         * 垫资状态：未支付
         */
        String REPAYSTATUS_STRING_UNUSED = "未支付";

        /**
         * 垫资状态：支付中
         */
        String REPAYSTATUS_STRING_USE = "支付中";
        /**
         * 垫资状态：支付成功
         */
        String REPAYSTATUS_STRING_SUCCESS = "支付成功";

        /**
         * 垫资状态：支付失败
         */
        String REPAYSTATUS_STRING_FAIL = "支付失败";

    }

    String CHAR_SET_UTF8 = "UTF-8";

    String MD5_TYPE = "MD5";

    /**
     * 阳光保险社会用信代码
     */
    String INSID = "91440300664161245Y";

    /**
     * 公共删除状态：已删除
     */
    Integer DELETED = 1;

    /**
     * 公共删除状态：未删除
     */
    Integer NOT_DELETE = 0;

    /**
     * 下划线符号
     */
    String UNDERLINE = "_";

    /**
     * 老订单转新订单redisKey
     */
    String OLD_ORDER_REDIS_KEY = "old:order:orgCode:";

    /**
     * 待分配
     */
    String ALLOCATION_WAIT = "1";

    /**
     * 分配中
     */
    String ALLOCATION_PROCESS = "2";

    /**
     * 协议
     */
    interface PageType {
        /**
         * 银行卡
         */
        Integer BANK = 1;

        /**
         * 支付宝
         */
        Integer ALIPAY = 2;
    }

    /**
     * 协议
     */
    interface Protocol {
        /**
         * 上架状态
         */
        String PUT_STATUS = "1";

        /**
         * 下架状态
         */
        String SOLD_OUT_STATUS = "9";
    }
    /**
     * 短信
     */
    interface Sms{
        /**
         * 机构短信告警开关：0关闭
         */
        Integer OFF = 0;
        /**
         * 机构短信告警开关：1开启
         */
        Integer ON = 1;
    }
    /**
     * 发布详情
     */
    interface Issue{
        /**
         * 发布详情：1关闭
         */
        Integer OFF = 1;
        /**
         * 发布详情：2开启
         */
        Integer ON = 2;
    }

    /**
     * 汽服主体配置详情
     */
    interface CarServiceConfig{

        /**
         * status:正常
         */
        Integer NORMAL = 0;

        /**
         * status:下架
         */
        Integer SOLD_OUT = 1;

    }

    /**
     * 汽服主体国金配置详情
     */
    interface CarServiceGuojinConfig{

        Integer SUCCESS = 1;

        Integer FAIL = 9;

        /**
         * 订单透传:开
         */
        Integer ORDERINFO_ON = 1;

        /**
         * 订单透传:关
         */
        Integer ORDERINFO_OFF = 0;

        /**
         * 订单核销:开
         */
        Integer ORDERVERIFICATION_ON = 1;

        /**
         * 订单核销:关
         */
        Integer ORDERVERIFICATION_OFF = 0;

    }

    interface HaihuiTransferConfig{

        Integer STATUS_NOMAL = 0;

        Integer STATUS_SOLD_OUT = 1;

        /**
         * 订单透传:开
         */
        Integer INIT_ON = 1;

        /**
         * 订单透传:关
         */
        Integer INIT_OFF = 0;

        /**
         * 订单核销:开
         */
        Integer VERIFICATION_ON = 1;

        /**
         * 订单核销:关
         */
        Integer VERIFICATION_OFF = 0;

    }

    /**
     * 访问记录状态
     */
    interface AccessRecord{

        Integer STATUS_NOMAL = 0;

        Integer STATUS_FAIL = 1;
    }

    interface OrganizeYgry{

        /**
         * 未启用
         */
        Integer STATUS_NOT_ENABLE = 0;

        /**
         * 已启用
         */
        Integer STATUS_ENABLE = 1;

        /**
         * 维护状态：正常
         */
        Integer PRESERVE_STATUS_NORMAL = 0;

        /**
         * 维护状态：维护中
         */
        Integer PRESERVE_STATUS_PRESERVING = 1;

    }

    interface AlipayAccountCertifiiedRecord{

        /**
         * 未注册
         */
        Integer UNREGISTERED= 0;


        /**
         * 已注册
         */
        Integer REGISTERED = 1;
    }

    interface RealNameCertifiiedRecord{

        /**
         *  认证失败
         */
        Integer UNAUTHORIZED = 0;

        /**
         *  未进行认证
         */
        Integer NOT_AUTHORIZED = 8;

        /**
         *  已认证
         */
        Integer AUTHENTICATED = 1;

        /**
         * 待认证
         */
        Integer WAIT_AUTHENTICAT = 2;

        /**
         *  异常
         */
        Integer EXCEPTION = 9;

    }

    interface Banner{

        /**
         * 轮播图
         */
        Integer CAROUSEL = 1;

        /**
         * 首页分类
         */
        Integer CLASSIFIED = 2;

        /**
         * 正常
         */
        Integer STATUS_NOMAL = 0;

        /**
         * 下线
         */
        Integer STATUS_OFFLINE = 1;

    }

    interface AlipaySwitch{

        Integer ON = 1;

        Integer OFF = 2;

    }





}
