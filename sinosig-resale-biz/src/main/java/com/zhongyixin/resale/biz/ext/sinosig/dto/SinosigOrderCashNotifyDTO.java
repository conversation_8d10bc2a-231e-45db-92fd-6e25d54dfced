package com.zhongyixin.resale.biz.ext.sinosig.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SinosigOrderCashNotifyDTO {

    /**
     * 渠道编码
     */
    private String source;

    /**
     * 积分商城配置的第三方机构
     */
    private String apiSource;

    /**
     * 服务包code
     */
    @JsonProperty("card_code")
    private String cardCode;

    /**
     * 服务包状态
     */
    @JsonProperty("service_status")
    private String serviceStatus = "1";

    /**
     * 服务包使用时间
     */
    @JsonProperty("service_exchange_time")
    private String serviceExchangeTime;

    /**
     * 签名类型
     */
    private String signType = "MD5";

    /**
     * 签名
     */
    private String sign;

    /**
     * 是否为子卡券的标识
     */
    @JsonProperty("is_child_coupon")
    private String isChildCoupon;

    @JsonProperty("child_package")
    private List<ChildPackage> childPackage;

    /**
     * 实名认证结果
     */
    private String realName;

    private String alipayAccount;

    private String writeOffWay;


    @Data
    public static class ChildPackage{

        /**
         * 子卡券券码
         */
        @JsonProperty("card_code")
        private String cardCode;

        /**
         * 子服务编码
         */
        @JsonProperty("service_item_code")
        private String serviceItemCode;


        @JsonProperty("service_item_name")
        private String serviceItemName;

        /**
         * 子服务可用次数
         */
        private String count;

        /**
         * 子服务已用次数
         */
        @JsonProperty("use_count")
        private String useCount;

        /**
         * 服务有效期开始时间
         */
        @JsonProperty("service_eff_date")
        private String serviceEffDate;

        @JsonProperty("service_exp_date")
        private String serviceExpDate;

    }

}
