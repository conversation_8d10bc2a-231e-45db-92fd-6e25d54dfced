package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.IntefaceInfo;

/**
 * <p>
 * 请求接口信息 服务类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface IntefaceInfoService extends IService<IntefaceInfo> {

    /**
     * 补齐响应信息
     * @param intefaceInfo 请求id
     * @return
     */
    Integer updateIntefaceInfoByRequestId(IntefaceInfo intefaceInfo);

}
