package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 下载数据管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_download_data_manage")
public class DownloadDataManage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 时间
     */
    private LocalDateTime dateTime;

    /**
     * 菜单层级
     */
    private String menuModel;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 1.处理中 2.处理成功 9.处理失败
     */
    private Integer status;

    /**
     * 路径
     */
    private String path;

    /**
     * 备注
     */
    private String remark;


}
