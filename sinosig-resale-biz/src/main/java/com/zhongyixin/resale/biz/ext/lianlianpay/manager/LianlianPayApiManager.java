package com.zhongyixin.resale.biz.ext.lianlianpay.manager;


import com.zhongyixin.resale.biz.ext.lianlianpay.dto.PaymentDTO;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.QueryPaymentOut;

/**
 * 连连支付相关接口
 * <AUTHOR>
 * @create 2023/2/24 16:33
 */
public interface LianlianPayApiManager {

    /**
     * 实时付款接口
     * @param paymentDTO 用户信息
     * @return response
     * @throws Exception
     */
    PaymentOut payment(PaymentDTO paymentDTO) throws Exception;

    /**
     * 实时付款查询接口
     * @param orderNo 订单id
     * @return response
     */
    QueryPaymentOut queryPayment(String orderNo);

    /**
     * 接口验签
     * @param jsonStr 对象json String
     * @param sign 签名
     * @return response
     */
    Boolean verifySign(String jsonStr,String sign);


}
