package com.zhongyixin.resale.biz.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光还款批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_sunshine_repayment_batch")
public class SunshineRepaymentBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ExcelIgnore
    private Integer id;

    /**
     * 还款批次号
     */
    @ExcelProperty("还款批次号")
    private String repayBatchNo;

    /**
     * 第三方平台业务号
     */
    @ExcelIgnore
    private String platRepaymentNo;

    /**
     * 文件摘要
     */
    @ExcelIgnore
    private String fileSign;

    /**
     * 上传人
     */
    @ExcelProperty("上传人")
    private String uploadBy;

    /**
     * oss文件地址
     */
    @ExcelIgnore
    private String ossPath;

    /**
     * 还款总金额
     */
    @ExcelProperty("还款总金额/元")
    private BigDecimal totalAmount;

    /**
     * 还款总数量
     */
    @ExcelProperty("还款总数量")
    private Integer totalCount;

    /**
     * 还款机构
     */
    @ExcelIgnore
    private String orgCode;

    /**
     * 机构名称
     */
    @ExcelProperty("还款机构")
    private String orgName;

    /**
     * 状态(0.初始化 1.上传成功 2.上传失败)
     */
    @ExcelProperty("状态")
    private Integer status;

    /**
     * 状态(0.初始化 1.上传成功 2.上传失败)
     */
    @ExcelProperty("状态")
    private Integer associationStatus;

    /**
     * 创建时间
     */
    @ExcelProperty("还款发起时间")
    private LocalDateTime createTime;

    /**
     * 止息日
     */
    @ExcelIgnore
    private String endRateDate;

    /**
     * 重试次数
     */
    @ExcelIgnore
    private Integer retryCount;

    /**
     * 失败原因
     */
    @ExcelIgnore
    private String memo;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("含税总金额")
    private BigDecimal taxTotalAmount;


}
