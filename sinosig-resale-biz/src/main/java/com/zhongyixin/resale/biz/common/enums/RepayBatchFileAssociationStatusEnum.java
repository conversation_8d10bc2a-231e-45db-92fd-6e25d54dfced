package com.zhongyixin.resale.biz.common.enums;

/**
 * <AUTHOR>
 */
public enum RepayBatchFileAssociationStatusEnum {

    //状态码
    FILE_ASSOCIATIONING(0, "文件关联申请初始化"),
    FILE_ASSOCIATION_FAIL(8, "文件关联申请失败"),
    FILE_ASSOCIATION_EXCEPTION(9, "文件关联申请异常"),
    FILE_ASSOCIATION_SUCCESS(2, "文件关联申请成功");


    public final Integer status;

    public final String msg;


    RepayBatchFileAssociationStatusEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public Integer getCode() {
        return status;
    }

    public String getMsg() {
        return msg;
    }

}
