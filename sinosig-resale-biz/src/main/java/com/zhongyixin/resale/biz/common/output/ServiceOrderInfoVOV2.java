package com.zhongyixin.resale.biz.common.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.zhongyixin.resale.biz.common.excel.converter.LocalDateConverter;
import com.zhongyixin.resale.biz.common.excel.converter.RepayStatusConverter;
import com.zhongyixin.resale.biz.common.sensitive.Sensitive;
import com.zhongyixin.resale.biz.common.sensitive.SensitiveStrategy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ServiceOrderInfoVOV2 {

    /**
     * id
     */
    @ExcelIgnore
    private Integer id;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(15)
    private LocalDateTime createTime;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    @ColumnWidth(15)
    private String orderId;

    /**
     * 金额
     */
    @ExcelProperty("金额")
    @ColumnWidth(15)
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    @ExcelProperty("用户姓名")
    @ColumnWidth(15)
    @Sensitive(strategy = SensitiveStrategy.USERNAME)
    private String userName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @ColumnWidth(15)
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String telephone;

    /**
     * 银行卡号
     */
    @Sensitive(strategy = SensitiveStrategy.BANK_CARD)
    @ExcelProperty("卡号")
    @ColumnWidth(15)
    private String bankCardNo;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    @ColumnWidth(15)
    @Sensitive(strategy = SensitiveStrategy.ID_CARD)
    private String idCard;

    /**
     * 支付成功时间
     */
    @ExcelProperty("支付成功时间")
    @ColumnWidth(15)
    private LocalDateTime tranDate;

    /**
     * 支付商编
     */
    @ExcelProperty("支付商编")
    @ColumnWidth(15)
    private String paymentNo;

    /**
     * 支付机构
     */
    @ExcelProperty("核销渠道")
    @ColumnWidth(15)
    private String payCompany;

    /**
     * 父级机构代码
     */
    @ExcelProperty("二级机构代码")
    @ColumnWidth(15)
    private String orgParentCode;

    /**
     * 机构代码
     */
    @ExcelProperty("三级机构代码")
    @ColumnWidth(15)
    private String orgCode;

    /**
     * 服务商
     */
    @ExcelProperty("服务商")
    @ColumnWidth(15)
    private String providerName;

    /**
     * 还款操作日
     */
    @ExcelProperty("还款操作日")
    @ColumnWidth(15)
    private LocalDateTime repayDate;

    /**
     * 还款批次号
     */
    @ExcelProperty("还款批次号")
    @ColumnWidth(15)
    private String repayId;

    /**
     * 止息日
     */
    @ExcelProperty(value = "止息日",converter = LocalDateConverter.class)
    @ColumnWidth(15)
    private LocalDate endRateDate;

    /**
     * 垫资状态
     */
    @ExcelProperty(value = "垫资状态",converter = RepayStatusConverter.class)
    @ColumnWidth(15)
    private Integer repayStatus;

    /**
     * 垫资天数
     */
    @ExcelProperty("垫资天数")
    @ColumnWidth(15)
    private Integer advanceFundDay;

    /**
     * 利息
     */
    @ExcelProperty(value = "利息")
    @ColumnWidth(15)
    private BigDecimal interest;

    /**
     * 碳减排量
     */
    private String carbonReduction;

    /**
     * 行驶里程
     */
    private String travlledDistance;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车型号
     */
    private String factoryBrandModel;

    /**
     * 车辆类型
     */
    private String carType;


}
