package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.NotifyJob;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06 20:00:23
 */
public interface NotifyJobMapper extends BaseMapper<NotifyJob> {

    /**
     * 查询待调度
     * @return
     */
    List<NotifyJob> queryWaitScheduled();

    /**
     * 修改
     * @param job
     * @return
     */
    Integer updateNotifyJob(NotifyJob job);


    /**
     * 根据code和orderId 查询job
     * @param code
     * @param orderId
     * @return
     */
    NotifyJob queryJobByCodeAndOrderId(@Param("code") String code,@Param("orderId") String orderId);

    NotifyJob selectOneByBusinessCodeAndOrderId(@Param("businessCode") String businessCode, @Param("orderId") String orderId);
}
