package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VinInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 厂牌型号
     */
    private String factoryBrandModel;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 1.已使用  0.未使用
     */
    private Integer status;


}
