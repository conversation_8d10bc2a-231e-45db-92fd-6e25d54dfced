package com.zhongyixin.resale.biz.common.constant;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/4/29 11:11
 */
@Data
public class ExtOrderInfoDTO {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 乐享语驾卡号
     */
    private String couponNo;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 客户类型
     */
    private String orderType;

    /**
     * 第三方渠道订单id
     */
    private String cardOrderNo;

    /**
     * 代付企业
     */
    private String payCompany;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String telphone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 保单号后六位
     */
    private String insurancestr;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 可用积分
     */
    private BigDecimal point;

    /**
     * 兑换金额
     */
    private BigDecimal amount;

    /**
     * 支付方式(银行卡(后期可以配置)   支付宝（不可配置）   )
     */
    private String payWay;

    /**
     * 状态 0.未支付 1.支付中  2.支付成功  9.支付失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 失败原因
     */
    private String failExplain;

    /**
     * 外部交易号
     */
    private String extTradeNo;

    /**
     * 订单来源
     */
    private String platformCode;

}
