package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.common.input.DataScopeDTO;
import com.zhongyixin.resale.biz.entity.SysDept;

/**
 * <p>
 * 系统部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface SysDeptService extends IService<SysDept> {

    /**
     * 获取当前用户下的权限
     * @param userId
     * @return
     */
    DataScopeDTO selectDataPermissionByUserId(Integer userId);

    String findSecondOrgAncestors( String orgCode);

    SysDept findDeptByDeptName( String deptName);

    void changeSecondOrgAncestors( String orgCode);

}
