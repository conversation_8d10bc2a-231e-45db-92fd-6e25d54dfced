package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.ServiceOrderConstant;
import com.zhongyixin.resale.biz.common.constant.WarnConstant;
import com.zhongyixin.resale.biz.common.constant.YuJiaConstant;
import com.zhongyixin.resale.biz.common.converter.SinosigDtoConvert;
import com.zhongyixin.resale.biz.common.input.SinosigNotifyOrderReceiveDTO;
import com.zhongyixin.resale.biz.common.util.IpUtils;
import com.zhongyixin.resale.biz.common.util.ServletUtils;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderReceiveDTO;
import com.zhongyixin.resale.biz.ext.sinosig.enums.ResultEnum;
import com.zhongyixin.resale.biz.ext.sinosig.manage.SinosigApiManager;
import com.zhongyixin.resale.biz.ext.sinosig.output.SinosigOrderCreateOutput;
import com.zhongyixin.resale.biz.service.OrderService;
import com.zhongyixin.resale.biz.service.ServiceOrderService;
import com.zhongyixin.resale.biz.service.SinosigNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 16:17
 */
@Slf4j
@Service
public class SinosigNotifyServiceImpl implements SinosigNotifyService {

    @Autowired
    SinosigApiManager sinosigApiManager;

    @Autowired
    private ServiceOrderService serviceOrderService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private LockManager lockManager;

    @Override
    @Transactional
    public SinosigNotifyOrderReceiveDTO receiveSinosigOrder(SinosigOrderReceiveDTO sinosigOrderPushDTO) {
        JSONObject jsonObject = JSONObject.getInstance();
        log.info("sinosig order receive. params：{}", jsonObject.toJSONString(sinosigOrderPushDTO));

        Map<String, Object> map = jsonObject.parseMap(jsonObject.toJSONString(sinosigOrderPushDTO), String.class, Object.class);

        SinosigOrderCreateOutput sinosigOrderCreateOutput = new SinosigOrderCreateOutput();
        SinosigNotifyOrderReceiveDTO sinosigNotifyOrderReceiveDTO = new SinosigNotifyOrderReceiveDTO();
        //验签
//        boolean verify = sinosigApiManager.verifyInitialSortSign(map);
//        if (!verify) {
//            log.info("[{}] sinosig sign error. code: {}, desc: {}", WarnConstant.BUSINESS_WARN,
//                    ResultEnum.SIGN_ERROR.getCode(), ResultEnum.SIGN_ERROR.getMsg());
//            sinosigOrderCreateOutput.setResultCode(ResultEnum.SIGN_ERROR.getCode());
//            sinosigOrderCreateOutput.setResultDesc(ResultEnum.SIGN_ERROR.getMsg());
//
//            sinosigNotifyOrderReceiveDTO.setSinosigOrderCreateOutput(sinosigOrderCreateOutput);
//            sinosigNotifyOrderReceiveDTO.setSendEventEnabled(false);
//            return sinosigNotifyOrderReceiveDTO;
//        }

        //新增订单
        ServiceOrder serviceOrder = SinosigDtoConvert.INSTANCE.sinosigOrderReceiveDTOToServiceOrder(sinosigOrderPushDTO);
        // 获取ip
        String ipAddr = IpUtils.getIpAddr(ServletUtils.getRequest());
        serviceOrder.setInCompanyIp(ipAddr);
        serviceOrder.setChannel(ServiceOrderConstant.Channel.SINOSIG);
        serviceOrder.setCouponNo(UUID.fastUUID().toString(true));
        serviceOrder.setStatus(ServiceOrderConstant.Status.STATUS_UNUSED);
        serviceOrder.setRepayStatus(ServiceOrderConstant.RepayStatus.REPAY_STATUS_UNCAPITAL);
        LocalDateTime createTime = LocalDateTime.now();
        serviceOrder.setCreateTime(createTime);
        serviceOrder.setSunshineServiceCharge(ServiceOrderConstant.Charge.SERVICECHARGE);
        serviceOrder.setOrderMode(ServiceOrderConstant.OrderMode.CLOSED_LOOP);
        // 判断是否是代理订单
        if (StrUtil.isNotBlank(serviceOrder.getAgentCode())
                && StrUtil.isNotBlank(serviceOrder.getAgentName())) {
            serviceOrder.setOrderMode(ServiceOrderConstant.OrderMode.AGENCY);
        }
        try {
            serviceOrderService.save(serviceOrder);
        }catch (DuplicateKeyException e){
            log.error("阳光订单生成错误,订单号：{}", serviceOrder.getOrderId());
            sinosigOrderCreateOutput.setResultCode(ResultEnum.REPEAT_ORDER_ERROR.getCode());
            sinosigOrderCreateOutput.setResultDesc(ResultEnum.REPEAT_ORDER_ERROR.getMsg());

            ServiceOrder serviceOrderInDb = serviceOrderService.selectServiceOrderByOrderId(serviceOrder.getOrderId());
            sinosigOrderCreateOutput.setServerPackage(createServicePackage(serviceOrderInDb));
            sinosigNotifyOrderReceiveDTO.setSinosigOrderCreateOutput(sinosigOrderCreateOutput);
            log.info("重复订单：{}",jsonObject.toJSONString(sinosigNotifyOrderReceiveDTO));
            sinosigNotifyOrderReceiveDTO.setSendEventEnabled(false);
            return sinosigNotifyOrderReceiveDTO;
        }

        //新增业务订单
        String orderLockKey = YuJiaConstant.Client.SUNSHINE + YuJiaConstant.COLON + "order"+ YuJiaConstant.COLON + "ygbh"
                + YuJiaConstant.COLON + serviceOrder.getOrderId();

        //创建订单
        //锁等待3s
        int lockWaitTime = 30;
        //锁最大释放时间60s
        int lockLeaseTime = 60;

        //TODO 请确认锁是否正确
        DLock lock = lockManager.getLock(orderLockKey);
        Order orderByApiSource = lock.tryLock(lockWaitTime, TimeUnit.SECONDS,
                () -> orderService.createOrderByServiceOrder(serviceOrder), () -> {
                    log.error("wait timeout, lock key:  {}", orderLockKey);
                    throw new RuntimeException("wait timeout, create order fail");
                });

        log.info("阳光订单生成成功,订单号：{}", serviceOrder.getOrderId());

        sinosigOrderCreateOutput.setResultCode(ResultEnum.SUCC.getCode());
        sinosigOrderCreateOutput.setResultDesc(ResultEnum.SUCC.getMsg());
        sinosigOrderCreateOutput.setServerPackage(createServicePackage(serviceOrder));

        sinosigNotifyOrderReceiveDTO.setOrder(orderByApiSource);
        sinosigNotifyOrderReceiveDTO.setServiceOrder(serviceOrder);
        sinosigNotifyOrderReceiveDTO.setOrgName(sinosigOrderPushDTO.getOrgName());
        sinosigNotifyOrderReceiveDTO.setSinosigOrderCreateOutput(sinosigOrderCreateOutput);
        sinosigNotifyOrderReceiveDTO.setSendEventEnabled(true);
        return sinosigNotifyOrderReceiveDTO;
    }

    public SinosigOrderCreateOutput.ServicePackage createServicePackage(ServiceOrder serviceOrder){
        SinosigOrderCreateOutput.ServicePackage servicePacakge = new SinosigOrderCreateOutput.ServicePackage();
        servicePacakge.setServiceEffDate(serviceOrder.getServiceEffDate());
        servicePacakge.setServiceExpDate(serviceOrder.getServiceExpDate());
        servicePacakge.setCardCode(serviceOrder.getCouponNo());
        servicePacakge.setCreateTime(serviceOrder.getCreateTime());
        return servicePacakge;
    }


}
