package com.zhongyixin.resale.biz.common.input;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR>
 * @createDate 2023/9/19 10:04
 */
@Data
public class OrgnzieQuatoQueryDTO {

    @NotBlank(message = "订单号不能为空")
    @Schema(defaultValue = "订单号")
    private String orderId;

    @NotBlank(message = "客户来源不能为空")
    @Schema(defaultValue = "客户来源")
    private String apiSource;

}
