package com.zhongyixin.resale.biz.ext.sinosig.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ResultEnum {

    //实时返回码
    SUCC("0000", "成功"),
    REPEAT_ORDER_ERROR("1007","重复订单"),
    SIGN_ERROR("1050", "签名错误"),
    SYSTEM_ERROR("9999", "未知错误"),
    NULL_FIELD_ERROR("9998","属性全为空值"),
    NULL_ORDERID_ERROR("9996","订单号为空"),
    USED_ORDER_ERROR("9995","订单已被使用");


    public final String code;

    public final String msg;


    ResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ResultEnum getRetCodeEnumByValue(String value) {
        for (ResultEnum retCodeEnum : ResultEnum.values()) {
            if (retCodeEnum.getCode().equals(value)) {
                return retCodeEnum;
            }
        }
        return null;
    }

}
