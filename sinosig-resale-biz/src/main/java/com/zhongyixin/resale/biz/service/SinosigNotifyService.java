package com.zhongyixin.resale.biz.service;

//import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderReceiveDTO;

import com.zhongyixin.resale.biz.common.input.SinosigNotifyOrderReceiveDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderReceiveDTO;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 15:58
 */
public interface SinosigNotifyService {

    /**
     * 接收阳光订单
     *
     * @return
     */
    SinosigNotifyOrderReceiveDTO receiveSinosigOrder(SinosigOrderReceiveDTO sinosigOrderPushDTO);




}
