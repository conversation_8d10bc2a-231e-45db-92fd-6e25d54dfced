package com.zhongyixin.resale.biz.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 阳光配置
 * <AUTHOR>
 * @create 2023/2/3 17:38
 */
@Data
@ConfigurationProperties(prefix = SinosigProperties.PREFIX)
public class SinosigProperties {

    public static final String PREFIX = "config.sinosig";

    /**
     * 域名或IP
     */
    private String domain;

    /**
     * 密钥
     */
    private String secret;

    private SecretPkg updateAndCancel;


    @Data
    public static class SecretPkg{
        private String extSecret;
        private String insSecret;
    }

}
