package com.zhongyixin.resale.biz.common.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UpdateOrganizeYgryAmountPeriodDTO implements Serializable {
    private static final long serialVersionUID = -4454299306492159075L;

    @Schema(description = "机构ID")
    private Integer orgId;

    /**
     * 总金额
     */
    @Schema(description = "额度")
    private BigDecimal totalAmount;

    /**
     * 限额周期
     */
    @Schema(description = "限额周期（单位：天）")
    private Integer quotaPeriod;

}
