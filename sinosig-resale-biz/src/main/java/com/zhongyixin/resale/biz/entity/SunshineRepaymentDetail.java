package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光批次明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_sunshine_repayment_detail")
public class SunshineRepaymentDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 还款批次号
     */
    private String repayBatchNo;

    /**
     * 阳光订单号
     */
    private String sunshineOrderId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
