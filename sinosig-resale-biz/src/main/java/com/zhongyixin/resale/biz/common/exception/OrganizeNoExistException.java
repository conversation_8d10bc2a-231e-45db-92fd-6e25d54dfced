package com.zhongyixin.resale.biz.common.exception;


import com.wftk.exception.common.ErrorCode;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.core.exception.BaseBusinessException;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/4/14 18:22
 */
public class OrganizeNoExistException extends BaseBusinessException {
    private static final long serialVersionUID = -9040723087491516359L;
    private int code;

    public OrganizeNoExistException() {
        this(GlobalErrorConstants.INTERNAL_SERVER_ERROR);
    }

    public OrganizeNoExistException(ErrorCode errorCode) {
        this(errorCode.code(), errorCode.message());
    }

    public OrganizeNoExistException(int code, String message) {
        super(message);
        this.code = code;
    }

    public OrganizeNoExistException(String message) {
        super(message);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public OrganizeNoExistException(Throwable throwable) {
        super(throwable);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
