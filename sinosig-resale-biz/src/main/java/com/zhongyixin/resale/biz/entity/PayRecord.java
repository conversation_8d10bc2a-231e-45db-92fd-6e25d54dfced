package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 支付记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_pay_record")
public class PayRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 支付通道
     */
    private String payCompany;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付商编
     */
    private String paymentNo;

    /**
     * 交易结果
     */
    private String transResult;


}
