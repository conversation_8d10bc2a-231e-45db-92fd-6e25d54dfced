package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.common.input.SinosigOrderCopyDTO;
import com.zhongyixin.resale.biz.entity.SinosigOrderInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 阳光订单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface SinosigOrderInfoMapper extends BaseMapper<SinosigOrderInfo> {

    Integer updateRepayInfoBySinosigOrderId(@Param("sinosigOrderIds") List<String> sinosigOrderIds, @Param("repayId") String repayId, @Param("endRateDate") LocalDate endRateDate);

    List<String> selectSinosigOrderIds(@Param("repayBatchNo") String repayBatchNo,
                                       @Param("pageNum") Integer pageNum,
                                       @Param("pageSize") Integer pageSize);

    Integer selectOrderIdsCount(@Param("repayBatchNo") String repayBatchNo);

    SinosigOrderInfo selectByOrderId(@Param("sinosigOrderId") String sinosigOrderId);

    Integer updateStartRateInfoBySinosigOrderId(@Param("sinosigOrderId") String sinosigOrderId, @Param("startRateDate") LocalDate startRateDate);

    List<SinosigOrderCopyDTO> selectAlipayServiceOrder();

    SinosigOrderInfo selectOneByOrderId(@Param("orderId") String orderId);


}
