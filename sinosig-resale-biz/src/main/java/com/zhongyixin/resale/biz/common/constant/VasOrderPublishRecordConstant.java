package com.zhongyixin.resale.biz.common.constant;

public interface VasOrderPublishRecordConstant {


    interface Status {

        /**
         * 待投放
         */
        Integer AWAIT = 0;

        /**
         * 处理中
         */
        Integer PROCESS = 1;

        /**
         * 成功
         */
        Integer SUCCESS = 2;

        /**
         * 失败
         */
        Integer FAIL = 3;

        /**
         * 异常
         */
        Integer EXCEPTION = 4;

    }

    interface OrderType {

        /**
         * 银行卡
         */
        Integer BANK = 1;

        /**
         * 支付宝
         */
        Integer ALIPAY = 2;
    }

}
