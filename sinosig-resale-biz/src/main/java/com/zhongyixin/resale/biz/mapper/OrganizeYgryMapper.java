package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhongyixin.resale.biz.common.annotation.DataPermission;
import com.zhongyixin.resale.biz.common.annotation.DataScopeType;
import com.zhongyixin.resale.biz.common.enums.DataScope;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.OrgOutput;
import com.zhongyixin.resale.biz.common.output.OrganizeAlipaySwitchVo;
import com.zhongyixin.resale.biz.common.output.OrganizeInfoVO;
import com.zhongyixin.resale.biz.common.output.OrganizeSummarizeVO;
import com.zhongyixin.resale.biz.entity.OrganizeYgry;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 阳光机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface OrganizeYgryMapper extends BaseMapper<OrganizeYgry> {

    /**
     * 查询机构
     * @param orgCode
     * @return
     */
    OrganizeYgry selectOrganize(String orgCode);


    /**
     * 查询机构额度
     * @param page
     * @param organizeSearchDTO
     * @return
     */
    IPage<OrganizeInfoVO> selectOrganizeInfo(IPage<OrganizeYgry> page , @Param("organizeSearchDTO") OrganizeSearchDTO organizeSearchDTO);

    /**
     * 查询二级机构列表
     * @return
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    List<OrganizeYgry> selectOrganizeSecondList();

    /**
     * 根据父级code查询三级级机构列表
     * @param orgCode
     * @return
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    List<OrganizeYgry> selectOrganizeListByOrgParentCode(String orgCode);

    /**
     * 根据父级code查询三级级机构列表
     * @param orgCode
     * @return
     */
    List<OrganizeYgry> selectNoPermissionOrganizeListByOrgParentCode(String orgCode);

    /**
     * 为机构添加额度
     * @param organizeAddFundDTO
     * @return
     */
    Integer changeOrganizeBanlance(OrganizeAddFundDTO organizeAddFundDTO);

    /**
     * 查询机构额度
     * @param orgCode
     * @return
     */
    BigDecimal selectTotalAmountByOrgCode(String orgCode);

    /**
     * 查询机构汇总垫资信息
     * @param page
     * @param organizeSummarizeDTO
     * @return
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "providerName" ,alias = "o"),
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "orgCode",alias = "o" )
    })
    IPage<OrganizeSummarizeVO> selectOrganizeSummarizeInfo(IPage<OrganizeYgry> page , @Param("organizeSummarizeDTO") OrganizeSummarizeDTO organizeSummarizeDTO);

    /**
     * 查询组织结构信息
     * @param organizeSearchDTO
     * @param page
     * @return 机构分页
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    IPage<OrganizeYgry> getOrgInfo(IPage<OrganizeYgry> page ,@Param("orgSearchDTO") OrganizeSearchDTO organizeSearchDTO);

    /**
     * 冻结金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer frozenAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    /**
     * 恢复金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer recoveryAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    /**
     * 扣减金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer deductionAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    /**
     * 维护机构额度
     * @param organizeAccountModifyDTO
     * @return
     */
    Integer safeguardOrganizeAccount(OrganizeAccountModifyDTO organizeAccountModifyDTO);

    /**
     * 恢复还款金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer rechargeRepayAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    Integer updatePreserveStatus(OrganizeYgry organizeYgry);

    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    List<OrgOutput> queryOrganizeInfo();

    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    IPage<OrganizeAlipaySwitchVo> selectSecondAlipaySwitchVoIPage(IPage<OrganizeAlipaySwitchVo> page, @Param("orgParentCode")String orgParentCode, @Param("alipaySwitch") Integer alipaySwitch);

    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
    })
    IPage<OrganizeAlipaySwitchVo> selectThirdAlipaySwitchVoIPage(IPage<OrganizeAlipaySwitchVo> page, @Param("orgParentCode")String orgParentCode, @Param("orgCode")String orgCode, @Param("alipaySwitch") Integer alipaySwitch);

    Integer updateAlipaySwitchByOrgParentCode(@Param("orgParentCode")String orgParentCode,@Param("alipaySwitch")Integer alipaySwitch);

    Integer updateAlipaySwitchByOrgCode(@Param("orgCode")String orgCode,@Param("alipaySwitch")Integer alipaySwitch);

    Integer selectCountByOrgParentCode(@Param("orgParentCode")String orgParentCode);

    Integer selectCountByOrgParentCodeAndAlipaySwitch(@Param("orgParentCode")String orgParentCode,@Param("alipaySwitch")Integer alipaySwitch);

}
