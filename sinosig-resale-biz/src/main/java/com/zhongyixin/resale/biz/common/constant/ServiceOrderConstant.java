package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 */
public interface ServiceOrderConstant {

    /**
     * 订单状态
     */
    interface Status {
        /**
         * 订单状态：未使用
         */
        Integer STATUS_UNUSED = 0;
        /**
         * 订单状态：处理中
         */
        Integer STATUS_PROCESSED = 1;
        /**
         * 订单状态：已使用
         */
        Integer STATUS_USED = 2;
        /**
         * 订单状态：失败
         */
        Integer STATUS_FAIL = 9;
    }

    /**
     * 垫资状态
     */
    interface RepayStatus {
        /**
         * 订单状态：未垫资
         */
        Integer REPAY_STATUS_UNCAPITAL = 0;
        /**
         * 订单状态：处理中
         */
        Integer REPAY_STATUS_PROCESSED = 1;
        /**
         * 订单状态：已垫资
         */
        Integer REPAY_STATUS_CAPITAL = 2;
        /**
         * 订单状态：已还款
         */
        Integer REPAY_STATUS_REPAY = 3;
    }

    interface Charge{

        /**
         * 服务费率
         */
        String SERVICECHARGE = "8.5";

    }

    interface Channel{

        Integer YUJIA = 1;

        Integer SINOSIG = 2;
    }

    interface OrderMode{
        /**
         * 闭环
         */
        Integer CLOSED_LOOP = 1;
        /**
         * 代理
         */
        Integer AGENCY = 2;
    }
}
