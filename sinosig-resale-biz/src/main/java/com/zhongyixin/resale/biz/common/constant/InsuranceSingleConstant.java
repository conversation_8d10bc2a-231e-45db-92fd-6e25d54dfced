package com.zhongyixin.resale.biz.common.constant;



public interface InsuranceSingleConstant {


    /**
     * 业务状态(1、进行中2、入库异常3、入库失败4、入库成功、5校验成功、6校验失败)
     */
    interface SERVICE_STATUS{


        /**
         * 进行中
         */
        Integer UNDERWAY = 1;
        /**
         * 入库异常
         */
        Integer INBOUND_EXCEPTION = 2;

        /**
         * 入库失败
         */
        Integer INBOUND_FAIL = 3;
        /**
         * 入库成功
         */
        Integer INBOUND_SUCCESS = 4;
        /**
         * 校验成功
         */
        Integer CHECK_SUCCESS = 5;

        /**
         * 校验失败
         */
        Integer CHECK_FAIL = 6;


    }


    /**
     * 操作状态（1、进行中2、操作中止3、操作完成）
     */
    interface OPERATION_STATUS{
        /**
         * 进行中
         */
        Integer UNDERWAY = 1;

        /**
         * 操作中止
         */
        Integer OPERATION_ABORT = 2;

        /**
         * 操作完成
         */
        Integer OPERATION_COMPLETE = 3;

    }


    /**
     * 类型（1、入库2、校验）
     */
    interface TYPE{
        /**
         * 入库
         */
        Integer PUTINSTORAGE = 1;
        /**
         * 校验
         */
        Integer VERIFY = 2;
    }


    /**
     * 业务类型（1、继续执行2、终止）
     */
    interface BUSINESS_TYPE{
        /**
         * 继续执行
         */
        Integer CONTINUE_TO_EXECUTE = 1;

        /**
         * 终止
         */
        Integer TERMINATION = 2;
    }



}
