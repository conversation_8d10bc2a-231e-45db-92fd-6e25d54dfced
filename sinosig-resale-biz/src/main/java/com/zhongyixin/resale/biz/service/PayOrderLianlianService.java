package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface PayOrderLianlianService extends IService<PayOrderLianlian> {

    /**
     * 根据支付Id查询民生订单
     * @param payOrderId
     * @return
     */
    PayOrderLianlian selectPayOrderLianlianByPayOrderId(String payOrderId);

    /**
     * 根据支付Id修改连连订单信息
     * @param payOrderLianlian
     * @return
     */
    Integer updatePayOrderLianlianByPayOrderId(PayOrderLianlian payOrderLianlian);

    /**
     * 根据订单ID查询尚未完成的订单
     * @param orderId
     * @return
     */
    PayOrderLianlian findUnfinishedOrder(String orderId);


    /**
     * 根据订单ID查询支付中或者已支付的订单
     * @param orderId
     * @return
     */
    PayOrderLianlian findPaidOrPayingOrder(String orderId);

    /**
     * 查询10分钟前的订单
     * @return
     */
    List<PayOrderLianlian> selectOrderByTenMinutes();

}
