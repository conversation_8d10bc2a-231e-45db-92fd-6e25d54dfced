package com.zhongyixin.resale.biz.ext.lianlianpay.bean;

import lombok.Data;

/**
 * 连连代付参数
 * <AUTHOR>
 */
@Data
public class PaymentRequest {

    /** 商户付款流水号 . */
    private String no_order;

    /** 商户付款时间 . */
    private String dt_order;

    /** 付款金额 . */
    private String money_order;

    /** 银行账号 . */
    private String card_no;

    /** 收款人姓名 . */
    private String acct_name;

    /** 收款人银行名称 . */
    private String bank_name;

    /** 付款用途. */
    private String info_order;

    /** 收款备注 用于给用户显示 . */
    private String memo;

    /** 对公对私标志 0-对私 1-对公 . */
    private String flag_card;

    /** 服务器异步通知地址 . */
    private String notify_url;

    /** 商户编号 . */
    private String oid_partner;

    /**
     * 开户行所在省市编码
     */
    private String city_code;

    /**
     * 银行编码
     */
    private String bank_code;

    /**
     * 开户支行名称
     */
    private String brabank_name;

    /** 用户来源 . */
    private String platform;

    /** api当前版本号 . */
    private String api_version;

    /** 签名方式 . */
    private String sign_type;

    /** 签名方 . */
    private String sign;
}
