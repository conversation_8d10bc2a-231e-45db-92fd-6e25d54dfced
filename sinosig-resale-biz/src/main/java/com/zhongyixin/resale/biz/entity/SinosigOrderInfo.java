package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光订单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SinosigOrderInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主订单号
     */
    private String orderId;

    /**
     * 阳光订单号
     */
    private String sinosigOrderId;

    /**
     * 卡号
     */
    private String couponNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 支付成功时间
     */
    private LocalDateTime tranDate;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 父级机构编码
     */
    private String orgParentCode;

    /**
     * 还款批次号
     */
    private String repayId;

    /**
     * 起息日
     */
    private LocalDate startRateDate;

    /**
     * 止息日
     */
    private LocalDate endRateDate;

    /**
     * 生产商
     */
    private String providerName;

    /**
     * 阳光服务费率
     */
    private BigDecimal sunshineServiceCharge;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除字段
     */
    private Integer deleted;

}
