package com.zhongyixin.resale.biz.config;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.zhongyixin.resale.biz.ext.sinosig.manage.DefaultSinosigApiManager;
import com.zhongyixin.resale.biz.ext.sinosig.manage.SinosigApiManager;
import com.zhongyixin.resale.biz.properties.SinosigProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 17:54
 */
@Configuration
@EnableConfigurationProperties(SinosigProperties.class)
public class SinosigConfiguration {

    @Bean
    SinosigApiManager sinosigApiManager(HttpRequestExecutor httpRequestExecutor, SinosigProperties sinosigProperties) {
        return new DefaultSinosigApiManager(httpRequestExecutor, sinosigProperties);
    }

//    @Bean
//    SinosigSecretService sinosigSecretService(SinosigProperties sinosigProperties){
//        return new SinosigSecretServiceImpl(sinosigProperties.getUpdateAndCancel());
//    }
}
