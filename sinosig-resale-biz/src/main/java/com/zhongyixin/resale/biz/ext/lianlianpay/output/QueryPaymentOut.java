package com.zhongyixin.resale.biz.ext.lianlianpay.output;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QueryPaymentOut {

    /** 请求结果代码 . */
    private String ret_code;

    /** 请求结果描述 . */
    private String ret_msg;

    /** 原请求中商户订单号 . */
    private String no_order;

    /** 商户编号 . */
    private String oid_partner;

    /** 连连支付单号 . */
    private String oid_paybill;

    /** 原请求中交易金额. */
    private String money_order;

    /** 订单状态，付款结果以订单状态为判断依据. */
    private String result_pay;

    /** 清算时间. */
    private String settle_date;

    /** 订单扩展字段 失败原因. */
    private String info_order;

    /** 商户付款时间 . */
    private String dt_order;

    /** 付款失败的原因，query_version为1.0时返回. */
    private String memo;

    /** 签名方式 . */
    private String sign_type;

    /** 签名方 . */
    private String sign;
}
