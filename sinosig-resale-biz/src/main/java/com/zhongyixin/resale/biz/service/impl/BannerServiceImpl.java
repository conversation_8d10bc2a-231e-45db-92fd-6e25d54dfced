package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.input.BannerDTO;
import com.zhongyixin.resale.biz.convert.BannerConvert;
import com.zhongyixin.resale.biz.entity.Banner;
import com.zhongyixin.resale.biz.mapper.BannerMapper;
import com.zhongyixin.resale.biz.service.BannerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements BannerService {

    @Resource
    BannerMapper bannerMapper;

    @Override
    public List<BannerDTO> selectListByType(Integer status, String apiSource, Integer type) {
        List<Banner> banners = bannerMapper.selectListByType(status,apiSource, type);
        return BannerConvert.INSTANCE.entityToDto(banners);
    }

    @Override
    public List<BannerDTO> selectListByTypes(Integer status, String apiSource, List<Integer> type) {
        List<Banner> banners = bannerMapper.selectListByTypes(status,apiSource, type);
        return BannerConvert.INSTANCE.entityToDto(banners);
    }

    @Override
    public List<BannerDTO> selectListByStatus(Integer status, String apiSource) {
        List<Banner> banners = bannerMapper.selectListByStatus(status,apiSource);
        return BannerConvert.INSTANCE.entityToDto(banners);
    }

}
