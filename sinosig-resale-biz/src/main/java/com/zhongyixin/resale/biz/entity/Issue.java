package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_issue")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Issue implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客户类型
     */
    private String apiSource;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发布开始时间
     */
    private LocalTime issueStartTime;

    /**
     * 发布结束时间
     */
    private LocalTime issueEndTime;

    /**
     * 状态 1.不需要发布 2.需要发布
     */
    private Integer status;

    /**
     * 内容提示
     */
    private String content;

    /**
     * 发布时间（天）
     */
    private LocalDate issueStartDate;

    /**
     * 发布时间（小时）
     */
    private LocalDate issueEndDate;

    /**
     * 类型
     */
    private String orderType;

    /**
     * 是否自动增加一天
     */
    private Integer autoAddDay;


}
