package com.zhongyixin.resale.biz.ext.sinosig.manage;

import cn.hutool.core.util.URLUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.http.client.core.common.type.DataType;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.wftk.http.client.core.request.HttpRequest;
import com.wftk.http.client.core.request.RequestMethod;
import com.wftk.http.client.core.request.builder.RequestBuilders;
import com.wftk.jackson.core.JSONObject;
import com.wftk.signature.builder.SignBuilder;
import com.zhongyixin.resale.biz.common.constant.NotifyJobConstant;
import com.zhongyixin.resale.biz.common.constant.OrderWriteOffConstant;
import com.zhongyixin.resale.biz.entity.NotifyJob;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.OrderWriteOff;
import com.zhongyixin.resale.biz.entity.SinosigNotifyRecord;
import com.zhongyixin.resale.biz.ext.sinosig.constant.SinosigConstant;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigDaySettleDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyResponseDTO;
import com.zhongyixin.resale.biz.ext.sinosig.enums.WriteOffWay;
import com.zhongyixin.resale.biz.ext.sinosig.output.SinosigSettleResult;
import com.zhongyixin.resale.biz.ext.sinosig.signature.SinosigInitialSortSignBuilder;
import com.zhongyixin.resale.biz.manage.CommonManager;
import com.zhongyixin.resale.biz.properties.SinosigProperties;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 17:49
 */
@Slf4j
public class DefaultSinosigApiManager implements SinosigApiManager{

    protected final String domain;

    protected final String secret;

    protected final HttpRequestExecutor requestExecutor;


    @Resource
    private SinosigNotifyRecordService sinosigNotifyRecordService;

    @Resource
    private NotifyJobService notifyJobService;

    @Resource
    CommonManager commonManager;

    @Resource
    OrderWriteOffService orderWriteOffService;

    public DefaultSinosigApiManager(HttpRequestExecutor httpRequestExecutor, SinosigProperties sinosigProperties) {
        this.domain = sinosigProperties.getDomain();
        this.secret = sinosigProperties.getSecret();
        this.requestExecutor = httpRequestExecutor;
    }

    protected SignBuilder getInitialSortSignBuilder(){
        return new SinosigInitialSortSignBuilder(secret);
    }

    protected <P, R> R execute(HttpRequest<P, R> httpRequest) {
        return requestExecutor.execute(httpRequest);
    }

    @Override
    public SinosigOrderCashNotifyResponseDTO cashNotify(SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO) {
        SignBuilder signBuilder = getInitialSortSignBuilder();
        JSONObject jsonObject = JSONObject.getInstance();
        Map<String, Object> paramMap = jsonObject.parseMap(jsonObject.toJSONString(sinosigOrderCashNotifyDTO), String.class, Object.class);
        HashMap<String, Object> signMap = new HashMap<>(paramMap);
        signMap.remove("child_package");
        String sign = signBuilder.addObject(signMap).build();
        paramMap.put("sign",sign);
        String url = getUrl(SinosigConstant.API.CASH_RETURN);
        HttpRequest<Map<String,Object>, SinosigOrderCashNotifyResponseDTO> httpRequest = RequestBuilders.<Map<String,Object>, SinosigOrderCashNotifyResponseDTO>bodyBuilder(url)
                .method(RequestMethod.POST)
                .json(paramMap)
                .resultType(new DataType<>() {
                }).build();
        SinosigOrderCashNotifyResponseDTO response = null;
        SinosigNotifyRecord sinosigNotifyRecord = new SinosigNotifyRecord();
        sinosigNotifyRecord.setOrderId(sinosigOrderCashNotifyDTO.getCardCode());
        sinosigNotifyRecord.setNotifyParams(JSONObject.getInstance().toJSONString(sinosigOrderCashNotifyDTO));

        try {
            response = execute(httpRequest);
            log.info("阳光核销通知请求参数:{} 返回参数：{}",sinosigOrderCashNotifyDTO,response);
            return response;
        }catch (Exception e){
            sinosigNotifyRecord.setExceptionMessage(e.getMessage());
            log.error("阳光核销通知异常",e);
            throw new BusinessException("阳光核销通知异常");
        }finally {
            //记录
            sinosigNotifyRecord.setResponseParams(JSONObject.getInstance().toJSONString(response));
            sinosigNotifyRecordService.save(sinosigNotifyRecord);
        }

    }

    @Override
    public SinosigSettleResult settlePush(SinosigDaySettleDTO sinosigDaySettleDTO) {
        SignBuilder signBuilder = getInitialSortSignBuilder();
        JSONObject jsonObject = JSONObject.getInstance();
        Map<String, Object> paramMap = jsonObject.parseMap(jsonObject.toJSONString(sinosigDaySettleDTO), String.class, Object.class);
        HashMap<String, Object> signMap = new HashMap<>(paramMap);
        String sign = signBuilder.addObject(signMap).build();
        paramMap.put("sign",sign);
        String url = getUrl(SinosigConstant.API.SETTLEMENT_PUSH);
        HttpRequest<Map<String,Object>, SinosigSettleResult> httpRequest = RequestBuilders.<Map<String,Object>, SinosigSettleResult>bodyBuilder(url)
                .method(RequestMethod.POST)
                .json(paramMap)
                .resultType(new DataType<>() {
                }).build();
        SinosigSettleResult response = null;
        log.info("阳光结算推送请求参数:{}",paramMap);
        try {
            response = execute(httpRequest);
            log.info("阳光结算推送返回参数：{}",response);
            return response;
        }catch (Exception e){
            log.error("阳光结算推送通知异常",e);
            throw new BusinessException("阳光结算推送通知异常");
        }

    }

    @Override
    public void doCashNotify(Order order, String voucherId, String businessCode, String bizType) {
        OrderWriteOff orderWriteOff = orderWriteOffService.selectOneByOrderId(order.getOrderId());
        String uniqueId = order.getOrderId();
        SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO = buildCashNotifyDTO(order);

        // double lock check
        NotifyJob notifyJob = notifyJobService.selectOneByBusinessCodeAndOrderId(businessCode,
                uniqueId);
        if(notifyJob != null){
            log.info("businessCode:{}, orderId:{}, 该notifyJob任务已存在",businessCode, uniqueId);
            return;
        }

        //先查询一次，有的话就不保存了,并且不通知了
        Integer notifyJobId = commonManager.saveNotifyJob(businessCode, uniqueId, sinosigOrderCashNotifyDTO);
        Integer orderStatus = null;

        NotifyJob notifyJobUpdate = new NotifyJob();
        if(notifyJobId > 0 ){
            notifyJobUpdate.setId(notifyJobId);
            try {
                SinosigOrderCashNotifyResponseDTO sinosigOrderCashNotifyResponseDTO = cashNotify(sinosigOrderCashNotifyDTO);
                if(!sinosigOrderCashNotifyResponseDTO.isSuccess()){
                    notifyJobUpdate.setStatus(NotifyJobConstant.Status.FAIL);
                    notifyJobUpdate.setEnded(NotifyJobConstant.Ended.NO);
                    if(businessCode.equals(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY)){
                        orderStatus = OrderWriteOffConstant.VasCouponStatus.FAIL;
                    }
                }else {
                    notifyJobUpdate.setStatus(NotifyJobConstant.Status.SUCCESS);
                    notifyJobUpdate.setEnded(NotifyJobConstant.Ended.YES);
                    notifyJobUpdate.setEndTime(new Date());
                    if(businessCode.equals(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY)){
                        orderStatus = OrderWriteOffConstant.VasCouponStatus.SUCCESS;
                    }
                }
                notifyJobService.updateById(notifyJobUpdate);
            }catch (Exception e){
                log.error("核销通知阳光异常",e);
                //异常的也要记录
                notifyJobUpdate.setStatus(NotifyJobConstant.Status.FAIL);
                notifyJobUpdate.setEnded(NotifyJobConstant.Ended.NO);
                notifyJobUpdate.setRemark(e.getMessage());
                notifyJobService.updateById(notifyJobUpdate);
                if(businessCode.equals(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY)){
                    orderStatus = OrderWriteOffConstant.VasCouponStatus.UNEXPECTED;
                }

            }
            if(orderWriteOff != null){
                if(orderStatus != null){
                    orderWriteOff.setOrderStatus(orderStatus);
                }
                log.info("update orderWriteOff:{}",orderWriteOff);
                orderWriteOffService.updateById(orderWriteOff);
            }
        }

    }

    @Override
    public boolean verifyInitialSortSign(Map<String, Object> paramsMap) {
        String sign = (String) paramsMap.get("sign");
        String buildSign = getInitialSortSignBuilder().addParams(paramsMap).build();
        return sign.equals(buildSign);
    }


    private SinosigOrderCashNotifyDTO buildCashNotifyDTO(Order order) {
        SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO = new SinosigOrderCashNotifyDTO();
        sinosigOrderCashNotifyDTO.setCardCode(order.getCouponNo());
        sinosigOrderCashNotifyDTO.setSource(order.getApiSource());
        sinosigOrderCashNotifyDTO.setApiSource(order.getApiSource());
        sinosigOrderCashNotifyDTO.setServiceStatus("1");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        sinosigOrderCashNotifyDTO.setServiceExchangeTime(LocalDateTime.now().format(dateTimeFormatter));
        WriteOffWay writeOffWay = WriteOffWay.getRetCodeEnumByLabel(order.getPayWay());
        if(writeOffWay != null){
            sinosigOrderCashNotifyDTO.setWriteOffWay(writeOffWay.code);
        }

        return sinosigOrderCashNotifyDTO;


    }


    protected String getUrl(String api) {
        return URLUtil.normalize(domain + api);
    }

    @Override
    public String getSign(Map<String, Object> paramsMap) {
        return getInitialSortSignBuilder().addParams(paramsMap).build();
    }
}
