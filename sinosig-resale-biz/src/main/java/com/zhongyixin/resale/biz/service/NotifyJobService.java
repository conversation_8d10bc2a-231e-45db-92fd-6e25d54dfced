package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.NotifyJob;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-06 20:00:23
 */
public interface NotifyJobService extends IService<NotifyJob> {


    /**
     * 查询待调度
     * @return
     */
   List<NotifyJob> queryWaitScheduled();

    /**
     * 修改
     * @param job
     * @return
     */
   Integer updateNotifyJob(NotifyJob job);


    /**
     * 根据code和orderId 查询job
     * @param code
     * @param orderId
     * @return
     */
   NotifyJob queryJobByCodeAndOrderId(String code,String orderId);


    NotifyJob selectOneByBusinessCodeAndOrderId(String businessCode,String orderId);


    /**
     *
     * @param businessCode
     * @param orderId
     * @param paramsJson
     * @return 0代表新建数据失败
     */
    Integer saveNotifyJob(String businessCode,String orderId,String paramsJson);

}
