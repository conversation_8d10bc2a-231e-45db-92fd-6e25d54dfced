package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统数据权限角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_sys_data_role")
public class SysDataRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "data_role_id", type = IdType.AUTO)
    private Integer dataRoleId;

    /**
     * 数据角色名称
     */
    private String dataRoleName;

    /**
     * 数据权限字符
     */
    private String dataKey;

    /**
     * 显示顺序
     */
    private Integer dataRoleSort;

    /**
     * 数据范围（1.全部数据权限 2.服务商数据权限 3.机构数据）
     */
    private Integer dataScope;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;


}
