package com.zhongyixin.resale.biz.common.output;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.zhongyixin.resale.biz.common.excel.converter.RepayStatusConverter;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 机构汇总VO
 * <AUTHOR>
 */
@Data
public class OrganizeSummarizeVO {

    /**
     * 机构代码
     */
    @ExcelProperty("机构代码")
    @ColumnWidth(15)
    private String orgCode;

    /**
     * 机构名称
     */
    @ExcelProperty("机构名称")
    @ColumnWidth(15)
    private String orgName;

    /**
     * 垫资笔数
     */
    @ExcelProperty("垫资笔数")
    @ColumnWidth(15)
    private Integer advanceFundCount;

    /**
     * 垫资金额
     */
    @ExcelProperty("垫资金额")
    @ColumnWidth(15)
    private BigDecimal amount;

    /**
     * 垫资状态
     */
    @ExcelProperty(value = "垫资状态",converter = RepayStatusConverter.class)
    @ColumnWidth(15)
    private Integer repayStatus;

    /**
     * 含税总金额
     */
    @ExcelProperty("含税总金额")
    @ColumnWidth(15)
    private BigDecimal taxAmount;

}
