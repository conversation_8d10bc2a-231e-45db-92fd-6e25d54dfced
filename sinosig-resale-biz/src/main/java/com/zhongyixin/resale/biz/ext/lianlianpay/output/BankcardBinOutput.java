package com.zhongyixin.resale.biz.ext.lianlianpay.output;

import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 2025/6/9 17:14
 */
@Data
public class BankcardBinOutput {

    private static final String PAY_SUCCESS = "0000";

    /** 请求结果代码 . */
    private String ret_code;

    /** 请求结果描述 . */
    private String ret_msg;

    /** 签名方式 . */
    private String sign_type;

    /** 签名方 . */
    private String sign;

    /** 所属银行名称 . */
    private String bank_name;

    /** 银行编码 . */
    private String bank_code;

    /**
     * 银行卡类型。2 - 储蓄卡。3 - 信用卡 .
     */
    private String card_type;

    /**
     * @return
     */
    public boolean successed() {
        return PAY_SUCCESS.equalsIgnoreCase(ret_code);
    }

}
