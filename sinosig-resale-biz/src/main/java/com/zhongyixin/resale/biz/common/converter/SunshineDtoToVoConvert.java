package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoQueryVO;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoVO;
import com.zhongyixin.resale.biz.entity.Order;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2023/2/9 17:40
 */
@Mapper
public abstract class SunshineDtoToVoConvert {

    public static SunshineDtoToVoConvert INSTANCE = Mappers.getMapper(SunshineDtoToVoConvert.class);

    /**
     * 阳光用户信息dto转阳光积分查询Input
     * @param sunshineUserInfoDTO 阳光用户信息dto
     * @return 阳光积分查询Input
     */
    @Mapping(source="telephone",target = "phone")
    public abstract SunshinePointQueryInput sunshineUserDtoToSunshinePointQuery(SunshineUserInfoDTO sunshineUserInfoDTO);

    /**
     * 阳光用户信息dto转阳光用户积分信息vo
     * @param sunshineUserInfoDTO 阳光用户信息dto
     * @return 阳光用户积分信息vo
     */
    public abstract SunshineUserPointInfoVO sunshineUserDtoToSunshineUserPointInfo(SunshineUserInfoDTO sunshineUserInfoDTO);

    /**
     * 阳光用户信息dto转阳光用户积分信息vo
     *
     * @param order 阳光用户信息dto
     * @return 阳光用户积分信息vo
     */
    @Mapping(source="telphone",target = "telephone")
    @Mapping(source="couponNo",target = "cardCode")
    @Mapping(source="cardOrderNo",target = "orderId")
    public abstract SunshineUserPointInfoQueryVO orderToSunshineUserPointInfo(Order order);

    /**
     * 阳光用户信息dto转阳光积分查询Input
     * @param sunshineConversionPointDTO 阳光用户信息dto
     * @return 阳光积分查询Input
     */
    @Mapping(source="telephone",target = "phone")
    public abstract SunshinePointQueryInput sunshineConversionPointDTOToSunshinePointQuery(SunshineConversionPointDTO sunshineConversionPointDTO);

    /**
     * 阳光用户兑换积分dto转订单
     * @param sunshineConversionPointDTO 阳光用户兑换积分dto
     * @return 订单
     */
    @Mapping(target = "bankCardNo",ignore = true)
    @Mapping(target = "orderId",ignore = true)
    @Mapping(source="orderId",target = "cardOrderNo")
    public abstract Order sunshineConversionPointDTOToOrder(SunshineConversionPointDTO sunshineConversionPointDTO);

    /**
     * 阳光用户兑换积分dto转订单
     * @param sunshinePaySuccessWriteOffInput 阳光用户兑换积分dto
     * @return 订单
     */
    public abstract SunbeamPaySuccessWriteOffInput sunshinePaySuccessWriteOffInputToSunbeamPaySuccessWriteOffInput(SunshinePaySuccessWriteOffInput sunshinePaySuccessWriteOffInput);
}
