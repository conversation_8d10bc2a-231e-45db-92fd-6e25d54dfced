package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.input.DataScopeDTO;
import com.zhongyixin.resale.biz.entity.SysDept;
import com.zhongyixin.resale.biz.mapper.SysDeptMapper;
import com.zhongyixin.resale.biz.service.SysDeptService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 系统部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    @Resource
    SysDeptMapper deptMapper;

    /**
     * 获取当前用户下的权限
     * @param userId
     * @return
     */
    @Override
    public DataScopeDTO selectDataPermissionByUserId(Integer userId) {
        return deptMapper.selectDataPermissionByUserId(userId);
    }

    @Override
    public String findSecondOrgAncestors(String orgCode) {
        return deptMapper.findSecondOrgAncestors(orgCode);
    }

    @Override
    public SysDept findDeptByDeptName(String deptName) {
        return deptMapper.findDeptByDeptName(deptName);
    }

    @Override
    public void changeSecondOrgAncestors(String orgCode) {
        String secondOrgAncestors = findSecondOrgAncestors(orgCode);
        String secondOrgName = deptMapper.findSecondOrgName(orgCode);
        SysDept deptByDeptName = findDeptByDeptName(secondOrgName);
        SysDept sysDept = new SysDept();
        sysDept.setAncestors(secondOrgAncestors);
        sysDept.setDeptId(deptByDeptName.getDeptId());
        updateById(sysDept);
    }
}
