package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.SysMenu;
import com.zhongyixin.resale.biz.entity.SysRoleMenu;

import java.util.List;

/**
 * <p>
 * 角色菜单关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * 获取所有权限
     * @param userId
     * @return
     */
    List<String> getMenuPermissionByUserId(long userId);

    /**
     * 获取所有URL资源
     * @return
     */
    List<SysMenu> getAllMenuURL();
}
