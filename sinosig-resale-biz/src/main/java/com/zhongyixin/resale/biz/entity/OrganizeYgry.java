package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_organize_ygry")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizeYgry implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 父级机构编码
     */
    private String orgParentCode;

    /**
     * 机构等级
     */
    private Integer level;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 限额周期
     */
    private Integer quotaPeriod;

    /**
     * 维护状态（0正常 1维护中）
     */
    private Integer preserveStatus;

    /**
     * 支付宝开关（1.开 2.关）
     */
    private Integer alipaySwitch;

    /**
     * 最早未还款的起息日
     */
    @TableField(exist = false)
    private String startRateDate;

}
