package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Data
@TableName("sys_log")
@EqualsAndHashCode(callSuper = false)
public class SysLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    private String userName; //用户名

    private String method; //方法名

    private String params; //参数

    private String ip; //ip地址

    private String url; //请求url

    private String type; //操作类型 :新增、删除等等

    private String model; //模块

    private LocalDateTime createDate; //操作时间

    private String result; //操作结果

    private String description;//描述


}
