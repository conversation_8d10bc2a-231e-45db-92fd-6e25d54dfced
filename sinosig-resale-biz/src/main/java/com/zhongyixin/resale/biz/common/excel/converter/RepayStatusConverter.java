package com.zhongyixin.resale.biz.common.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;


/**
 * <AUTHOR>
 */
public class RepayStatusConverter implements Converter<Integer> {

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    @Override
    public WriteCellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if(Integer.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED) == value) {
            return new WriteCellData<>(CommonConstant.ServiceOrder.REPAYSTATUS_STRING_UNUSED);
        }else if(Integer.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_PROCESSED) == value){
            return new WriteCellData<>(CommonConstant.ServiceOrder.REPAYSTATUS_STRING_PROCESSED);
        }else if(Integer.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED)== value){
            return new WriteCellData<>(CommonConstant.ServiceOrder.REPAYSTATUS_STRING_USED);
        }
        return new WriteCellData<>(CommonConstant.ServiceOrder.REPAYSTATUS_STRING_REPAYED);
    }



}
