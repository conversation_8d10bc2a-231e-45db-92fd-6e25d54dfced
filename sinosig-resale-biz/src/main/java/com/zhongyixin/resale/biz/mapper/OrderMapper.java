package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.Order;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 总订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据orderId查询订单
     *
     * @param orderId
     * @return
     */
    Order selectOrderByOrderId(String orderId);
    Order selectOrderByCardOrderNo(String orderId);

    /**
     * 根据渠道信息查询订单
     *
     * @param order
     * @return
     */
    Order selectOrderByApiSource(Order order);

    /**
     * 查询失败或初始渠道信息订单
     *
     * @param order
     * @return
     */
    Order selectFailAndInitOrderByApiSource(Order order);

    /**
     * 查询平安订单状态
     *
     * @param order
     * @return
     */
    String selectOrderStatusWithPingAn(Order order);

    /**
     * 查询国任订单状态
     *
     * @param order
     * @return
     */
    String selectOrderStatusWithGuoRen(Order order);

    /**
     * 查询渠道订单状态
     *
     * @param order
     * @return
     */
    String selectOrderStatusWithChannel(Order order);

    /**
     * 查询阳光订单状态
     * @param order
     * @return
     */
    String selectOrderStatusWithSunshine(Order order);

    /**
     * 根据id修改订单
     * 状态为初始化或失败
     *
     * @param order
     * @return
     */
    Integer updateOrderById(Order order);

    /**
     * 根据id修改订单
     * 状态为处理中
     * @param order
     * @return
     */
    Integer updateOrderByIdAndDealing(Order order);

    /**
     * 根据id修改订单uuid
     *
     * @param order
     * @return
     */
    Integer updateOrderUUIDById(Order order);

    /**
     * 根据订单id修改订单
     * 状态为处理中
     *
     * @param order
     * @return
     */
    Integer updateOrderByOrderId(Order order);

    /**
     * 取消订单
     * @param orderId
     * @return
     */
    Integer cancelOrder(String orderId);

    /**
     * 修改订单
     * @param dto
     * @return
     */
    Integer updateOrder(UpdateOrderDTO dto);

    Integer updateOrderPayWayByOrderId(Order order);


//    boolean validExtOrderCouponNo(@Param("couponNo")String couponNo,
//                                  @Param("couponNo")String couponNo,
//                                  @Param("platformCode")String platformCode);

    List<Order> selectListByStatus(@Param("status") Integer status);

    List<Order> selectNotPushRecordData();


}
