package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.mapper.ServiceOrderMapper;
import com.zhongyixin.resale.biz.service.ServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 阳光明细订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service
@Slf4j
public class ServiceOrderServiceImpl extends ServiceImpl<ServiceOrderMapper, ServiceOrder> implements ServiceOrderService {


    @Override
    public ServiceOrderCopyDTO selectServiceOrderCopyDTOByOrderId(String orderId) {
        return baseMapper.selectServiceOrderCopyDTOByOrderId(orderId);
    }

    /**
     * 查询订单
     *
     * @param orderId
     * @return
     */
    @Override
    public ServiceOrder selectServiceOrderByOrderId(String orderId) {
        return baseMapper.selectServiceOrderByOrderId(orderId);
    }
    @Override
    public boolean updateOrder(UpdateOrderDTO dto) {
        return baseMapper.updateOrder(dto) > 0;
    }

    @Override
    public Integer updateServiceOrderRepayStatus(ServiceOrder serviceOrder) {
        return baseMapper.updateServiceOrderRepayStatus(serviceOrder);
    }
}
