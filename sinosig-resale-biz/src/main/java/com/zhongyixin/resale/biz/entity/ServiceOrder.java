package com.zhongyixin.resale.biz.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光明细订单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_service_order")
public class ServiceOrder implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 卡密
     */
    private String couponNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    @ExcelProperty("用户姓名")
    @ColumnWidth(15)
    private String userName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @ColumnWidth(15)
    private String telephone;

    /**
     * 银行卡号
     */
    @ExcelIgnore
    private String bankCardNo;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    @ColumnWidth(15)
    private String idCard;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 支付成功时间
     */
    private LocalDateTime tranDate;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 父级机构代码
     */
    private String orgParentCode;

    /**
     * 代付商编
     */
    private String paymentNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 还款状态
     * ,converter = RepayStatusConverter.class
     */
    private Integer repayStatus;

    /**
     * 还款批次号
     */
    private String repayId;

    /**
     * 还款操作日
     */
    private LocalDateTime repayDate;

    /**
     * 起息日
     */
    private LocalDate startRateDate;

    /**
     * 止息日
     */
    private LocalDate endRateDate;

    /**
     * 汽服id
     */
    private String ppid;

    private String customerNo;

    /**
     * 服务生效时间
     */
    private LocalDateTime serviceEffDate;

    /**
     * 服务失效时间
     */
    private LocalDateTime serviceExpDate;

    /**
     * 生产商
     */
    private String providerName;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 客户来源payCompany
     */
    private String source;

    /**
     * 代付通道
     */
    private String payCompany;

    /**
     *  保司IP地址
     */
    private String inCompanyIp;

    /**
     *  保司社会信用代码
     */
    private String inCompanyCreditCode;

    /**
     * 阳光服务费率字段
     */
    private String sunshineServiceCharge;

    /**
     * 来源渠道：1.语驾；2.阳光;
     */
    private Integer channel;

    /**
     * 代理人编码
     */
    private String agentCode;

    /**
     * 代理人名称
     */
    private String agentName;

    /**
     * 订单模式 1：闭环，2：代理
     */
    private Integer orderMode;

}
