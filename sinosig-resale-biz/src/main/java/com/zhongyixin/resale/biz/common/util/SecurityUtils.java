//package com.zhongyixin.resale.biz.common.util;
//
//import com.qunqiu.payment.admin.model.dto.LoginUser;
//import com.qunqiu.payment.admin.model.entity.SysUser;
//import com.wftk.exception.core.exception.BusinessException;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
//
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 安全服务工具类
// *
// * <AUTHOR>
// */
//public class SecurityUtils
//{
//    /**
//     * 用户ID
//     **/
//    public static Integer getUserId()
//    {
//        try
//        {
//            return getLoginUser().getUserId();
//        }
//        catch (Exception e)
//        {
//            throw new BusinessException(HttpServletResponse.SC_UNAUTHORIZED,"获取用户ID异常");
//        }
//    }
//
//
//    /**
//     * 获取用户账户
//     **/
//    public static String getUsername()
//    {
//        try
//        {
//            return getLoginUser().getUsername();
//        }
//        catch (Exception e)
//        {
//            throw new BusinessException( HttpServletResponse.SC_UNAUTHORIZED,"获取用户账户异常");
//        }
//    }
//
//    /**
//     * 获取匿名用户账户
//     **/
//    public static String getAnonymityUsername()
//    {
//
//        try
//        {
//            return getLoginUser().getUsername();
//        }
//        catch (Exception e)
//        {
//            return "Anonymity";
////            throw new BusinessException( HttpServletResponse.SC_UNAUTHORIZED,"获取用户账户异常");
//        }
//    }
//
//    /**
//     * 获取用户
//     **/
//    public static LoginUser getLoginUser()
//    {
//        try
//        {
//            return (LoginUser) getAuthentication().getPrincipal();
//        }
//        catch (Exception e)
//        {
//            throw new BusinessException( HttpServletResponse.SC_UNAUTHORIZED,"获取用户信息异常");
//        }
//    }
//
//    public static LoginUser getAnonymityLoginUser()
//    {
//        try
//        {
//            return (LoginUser) getAuthentication().getPrincipal();
//        }
//        catch (Exception e)
//        {
//            LoginUser loginUser = new LoginUser();
//            SysUser sysUser = new SysUser();
//            sysUser.setUserName("Anonymity");
//            loginUser.setUser(sysUser);
//            return loginUser;
//        }
//    }
//
//    /**
//     * 获取Authentication
//     */
//    public static Authentication getAuthentication()
//    {
//        return SecurityContextHolder.getContext().getAuthentication();
//    }
//
//    /**
//     * 生成BCryptPasswordEncoder密码
//     *
//     * @param password 密码
//     * @return 加密字符串
//     */
//    public static String encryptPassword(String password)
//    {
//        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
//        return passwordEncoder.encode(password);
//    }
//
//    /**
//     * 判断密码是否相同
//     *
//     * @param rawPassword 真实密码
//     * @param encodedPassword 加密后字符
//     * @return 结果
//     */
//    public static boolean matchesPassword(String rawPassword, String encodedPassword)
//    {
//        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
//        return passwordEncoder.matches(rawPassword, encodedPassword);
//    }
//
//    /**
//     * 是否为管理员
//     *
//     * @param userId 用户ID
//     * @return 结果
//     */
//    public static boolean isAdmin(Long userId)
//    {
//        return userId != null && 1L == userId;
//    }
//}
