package com.zhongyixin.resale.biz.common.input;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024/3/8 10:39
 */

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrderConsumeNotityInput {


    /**
     * id
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 卡密
     */
    private String couponNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 支付成功时间
     */
    private LocalDateTime tranDate;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 父级机构代码
     */
    private String orgParentCode;

    /**
     * 代付商编
     */
    private String paymentNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 还款状态
     */
    private Integer repayStatus;

    /**
     * 还款批次号
     */
    private String repayId;

    /**
     * 还款操作日
     */
    private LocalDateTime repayDate;

    /**
     * 起息日
     */
    private LocalDate startRateDate;

    /**
     * 止息日
     */
    private LocalDate endRateDate;

    /**
     * 汽服id
     */
    private String ppid;

    private String customerNo;

    /**
     * 服务生效时间
     */
    private LocalDateTime serviceEffDate;

    /**
     * 服务失效时间
     */
    private LocalDateTime serviceExpDate;

    /**
     * 生产商
     */
    private String providerName;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 客户来源payCompany
     */
    private String source;

    /**
     * 代付通道
     */
    private String payCompany;

    /**
     *  保司IP地址
     */
    private String inCompanyIp;

    /**
     *  保司社会信用代码
     */
    private String inCompanyCreditCode;

    /**
     * 阳光服务费率字段
     */
    private String sunshineServiceCharge;

    /**
     * 来源渠道：1.语驾；2.阳光;
     */
    private Integer channel;

    /**
     * 代理人编码
     */
    private String agentCode;

    /**
     * 代理人名称
     */
    private String agentName;

    /**
     * 订单类型 1.闭环订单  2.代理订单
     */
    private Integer orderType;


}
