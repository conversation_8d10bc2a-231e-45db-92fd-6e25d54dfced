package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.common.input.BannerDTO;
import com.zhongyixin.resale.biz.entity.Banner;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/3/13 14:40
 */
@Mapper
public abstract class BannerConvert {

    public static BannerConvert INSTANCE = Mappers.getMapper(BannerConvert.class);


    public abstract BannerDTO entityToDto(Banner banner);

    public abstract List<BannerDTO> entityToDto(List<Banner> banner);


}
