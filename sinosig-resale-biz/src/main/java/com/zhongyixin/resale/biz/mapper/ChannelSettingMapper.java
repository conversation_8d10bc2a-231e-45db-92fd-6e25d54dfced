package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.ChannelSetting;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 渠道通道配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
public interface ChannelSettingMapper extends BaseMapper<ChannelSetting> {

    List<ChannelSetting> selectPageList();

    ChannelSetting selectOneByChannelCode(@Param("channelCode")String channelCode);

    int selectPageList_COUNT();

}
