package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.common.core.result.ApiResult;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.*;
import com.zhongyixin.resale.biz.common.converter.ServiceOrderConverter;
import com.zhongyixin.resale.biz.common.converter.SinosigDtoConvert;
import com.zhongyixin.resale.biz.common.converter.SunshineDtoToVoConvert;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.OrganizeYgryVO;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoQueryVO;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoVO;
import com.zhongyixin.resale.biz.entity.*;
import com.zhongyixin.resale.biz.manage.SunshineOrderManager;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 阳光服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Slf4j
@Service
public class SunshineBusinessServiceImpl implements SunshineBusinessService {

    @Resource
    OrderService orderService;

    @Resource
    ServiceOrderService serviceOrderService;

    @Resource
    ChannelSettingService channelSettingService;

    @Autowired
    IssueService issueService;

    @Autowired
    OrganizeYgryService organizeYgryService;

    @Autowired
    SunshineOrderExceptionRecordService sunshineOrderExceptionRecordService;

    @Autowired
    SunshineOrderManager sunshineOrderManager;

    @Autowired
    PayRecordService payRecordService;

    @Autowired
    PaymentService paymentService;

    @Autowired
    UserOperateInfoService userOperateInfoService;

    @Autowired
    PayOrderLianlianService payOrderLianlianService;

    @Autowired
    LockManager lockManager;

    @Autowired
    RedissonClient redissonClient;

    /**
     * 查询是否有发布计划（阳光）
     *
     * @param apiSource
     * @param orderType
     * @return
     */
    @Override
    public ApiResult<Issue> queryIssue(String apiSource, String orderType) {

        if (StrUtil.isBlank(apiSource)) {
            log.error("阳光渠道查询发布详情异常，apiSource为空");
            Issue issue = new Issue();
            issue.setStatus(CommonConstant.Issue.OFF);
            return ApiResult.ok(issue);
        }

        if (StrUtil.isBlank(orderType)) {
            log.error("阳光渠道查询发布详情异常，orderType为空");
            Issue issue = new Issue();
            issue.setStatus(CommonConstant.Issue.OFF);
            return ApiResult.ok(issue);
        }

        // 通道维护
        ChannelSetting channelSetting = channelSettingService.selectOneByChannelCode(apiSource);
        if(channelSetting != null){
            if(Objects.equals(channelSetting.getStatus(), ChannelSettingConstant.Status.MAINTENANCE)){
                // 维护中
                Issue issue = new Issue();
                issue.setStatus(CommonConstant.Issue.ON);
                issue.setContent(channelSetting.getRemark());
                return ApiResult.ok(issue);
            }
        }

        Issue issue = issueService.selectOneByApiSource(apiSource, orderType);

        if (issue == null) {
            issue = new Issue();
            issue.setStatus(CommonConstant.Issue.OFF);
        } else {
            // 判断时间
            LocalDate issueStartDate = issue.getIssueStartDate();
            LocalTime issueStartTime = issue.getIssueStartTime();

            LocalDate issueEndDate = issue.getIssueEndDate();
            LocalTime issueEndTime = issue.getIssueEndTime();

            LocalDateTime issueStartDateTime = LocalDateTime.of(issueStartDate, issueStartTime);
            LocalDateTime issueEndDateTime = LocalDateTime.of(issueEndDate, issueEndTime);
            LocalDateTime nowTime = LocalDateTime.now();

            if (!(nowTime.isAfter(issueStartDateTime) && nowTime.isBefore(issueEndDateTime))) {
                issue = new Issue();
                issue.setStatus(CommonConstant.Issue.OFF);
            }

        }

        return ApiResult.ok(issue);
    }

    @Override
    public ApiResult<OrganizeYgryVO> queryOrgQuato(OrgnzieQuatoQueryDTO orgnzieQuatoQueryDTO) {
        //查询订单
        ServiceOrderCopyDTO serviceOrderCopyDTO = serviceOrderService.selectServiceOrderCopyDTOByOrderId(orgnzieQuatoQueryDTO.getOrderId());
        ServiceOrder serviceOrder = ServiceOrderConverter.INSTANCE.copyDtoToEntity(serviceOrderCopyDTO);

        OrganizeYgryVO organizeYgryVO = new OrganizeYgryVO();

        if (serviceOrder == null) {
            log.error("阳光订单查询不存在，订单号：{}", orgnzieQuatoQueryDTO.getOrderId());
            organizeYgryVO.setStatus(1);
            organizeYgryVO.setMsg("订单异常，请联系管理员。");
            return ApiResult.ok(organizeYgryVO);
        }

        OrganizeYgry organizeYgry = organizeYgryService.selectOrganize(serviceOrder.getOrgParentCode());

        if (organizeYgry == null) {
            log.error("阳光机构查询不存在，机构编码：{}", serviceOrder.getOrgParentCode());
            organizeYgryVO.setStatus(2);
            organizeYgryVO.setMsg("机构异常，请联系管理员。");
            SunshineOrderExceptionRecord sunshineOrderExceptionRecord = new SunshineOrderExceptionRecord();
            sunshineOrderExceptionRecord.setMsg("阳光订单异常");
            sunshineOrderExceptionRecord.setApiSource(orgnzieQuatoQueryDTO.getApiSource());
            sunshineOrderExceptionRecord.setOrderId(orgnzieQuatoQueryDTO.getOrderId());
            sunshineOrderExceptionRecordService.add(sunshineOrderExceptionRecord);
            return ApiResult.ok(organizeYgryVO);
        }

        if (Objects.equals(organizeYgry.getStatus(), CommonConstant.OrganizeYgry.STATUS_NOT_ENABLE)) {
            log.error("阳光机构未启用，机构编码：{}", serviceOrder.getOrgParentCode());
            organizeYgryVO.setStatus(3);
            organizeYgryVO.setMsg("机构未启用，请联系管理员。");
            return ApiResult.ok(organizeYgryVO);
        }

        if (Objects.equals(organizeYgry.getPreserveStatus(), CommonConstant.OrganizeYgry.PRESERVE_STATUS_PRESERVING)) {
            log.error("阳光机构维护中，机构编码：{}", serviceOrder.getOrgParentCode());
            organizeYgryVO.setStatus(4);
            organizeYgryVO.setMsg("机构维护中，请联系管理员。");
            return ApiResult.ok(organizeYgryVO);
        }

        organizeYgryVO.setStatus(0);
        organizeYgryVO.setMsg("正常");
        return ApiResult.ok(organizeYgryVO);
    }


    @Override
    public ApiResult<SunshineUserPointInfoQueryVO> querySinosigOrderPointLogicDeal(SunshineUserInfoQueryDTO sunshineUserInfoQueryDTO) {
        //查询阳光订单状态
        Order order = new Order();
        order.setCouponNo(sunshineUserInfoQueryDTO.getCardCode());
        order.setCardOrderNo(sunshineUserInfoQueryDTO.getOrderId());
        order.setApiSource(sunshineUserInfoQueryDTO.getApiSource());
        Order orderInDb = orderService.selectOrderByApiSource(order);
        if (orderInDb == null) {
            return ApiResult.fail(500,"订单不存在!");
        }

        String statusStr = orderInDb.getStatus().toString();

        if (OrderConstant.EXCEPTION_ORDER_STATUS.equals(statusStr)) {
            return ApiResult.fail(500,"订单异常!!");
        }

        if (OrderConstant.CANCLE_ORDER_STATUS.equals(statusStr)) {
            return ApiResult.fail(500,"订单不存在");
        }

        if (!OrderConstant.OrderType.NEW_YGBH.equals(orderInDb.getOrderType())) {
            return ApiResult.fail(500,"订单不存在");
        }

        // 查询service订单 和 机构
        ServiceOrder serviceOrder = serviceOrderService.selectServiceOrderByOrderId(orderInDb.getCardOrderNo());
        if (serviceOrder == null) {
            log.info("serviceOrder 不存在， orderId：{}", orderInDb.getCardOrderNo());
            return ApiResult.fail(500,"订单不存在");
        }
        LocalDateTime nowTime = LocalDateTime.now();
        if (nowTime.isAfter(serviceOrder.getServiceExpDate()) || nowTime.isBefore(serviceOrder.getServiceEffDate())) {
            log.info("serviceOrder未在有效时间范围内， orderId：{}", orderInDb.getCardOrderNo());
            return ApiResult.fail(500,"订单不存在");
        }

        if (orderInDb.getPayWay().equals(OrderConstant.PayWay.ALIPAY)) {
            OrganizeYgry organizeYgry = organizeYgryService.selectOrganize(serviceOrder.getOrgCode());
            if (Objects.equals(organizeYgry.getAlipaySwitch(), CommonConstant.AlipaySwitch.OFF)) {
                // 更换支付方式
                //TODO 是否还要修改
//                changePayWay(orderInDb, serviceOrder, OrderConstant.PayWay.BANK_CARDS);
            }
        }

        SunshineUserPointInfoQueryVO sunshineUserPointInfoVO = SunshineDtoToVoConvert.INSTANCE.orderToSunshineUserPointInfo(orderInDb);
        sunshineUserPointInfoVO.setRedirectUrl(sunshineUserInfoQueryDTO.getRedirectUrl());
        sunshineUserPointInfoVO.setBalance(String.valueOf(orderInDb.getAmount()));
        if (OrderConstant.FAIL_ORDER_STATUS.equals(statusStr)) {
            PayRecord payRecord = payRecordService.selectLastTimeOne(orderInDb.getOrderId(), Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
            if (payRecord != null) {
                String payCompany = OrderConstant.PayWay.BANK_CARDS;
                if (payRecord.getPayCompany().contains(OrderConstant.PayWay.ALIPAY)) {
                    payCompany = OrderConstant.PayWay.ALIPAY;
                }
                sunshineUserPointInfoVO.setLastTimePayWay(payCompany);
            }
        }

        return ApiResult.ok(sunshineUserPointInfoVO);
    }


    @Override
    public String receiveCardRolls(SunshineReceiveCardRollsDTO sunshineReceiveCardRollsDTO) {
        log.info("Sunshine receiveCardRolls sunshineReceiveCardRollsDTO：{}", sunshineReceiveCardRollsDTO);
        //校验开关信息
        ChannelSetting channelSetting = channelSettingService.selectOneByChannelCode(sunshineReceiveCardRollsDTO.getApiSource());
        if(channelSetting != null && Objects.equals(channelSetting.getStatus(), ChannelSettingConstant.Status.MAINTENANCE)){
            throw new BusinessException(channelSetting.getRemark());
        }

        String orderId = sunshineReceiveCardRollsDTO.getOrderId();
        Order orderByApiSource = new Order();
        orderByApiSource.setCouponNo(sunshineReceiveCardRollsDTO.getCardCode());
        orderByApiSource.setCardOrderNo(orderId);
        orderByApiSource.setApiSource(sunshineReceiveCardRollsDTO.getApiSource());
        Order order = orderService.selectOrderByApiSource(orderByApiSource);
        if (BeanUtil.isEmpty(order)) {
            throw new BusinessException("订单不存在");
        }
        if (!OrderConstant.OrderType.NEW_YGBH.equals(order.getOrderType())) {
            throw new BusinessException("订单不存在");
        }
        ServiceOrderCopyDTO serviceOrderCopyDTO = serviceOrderService.selectServiceOrderCopyDTOByOrderId(orderId);
        ServiceOrder serviceOrder = SinosigDtoConvert.INSTANCE.toServiceOrder(serviceOrderCopyDTO);
        if (BeanUtil.isEmpty(serviceOrder)) {
            throw new BusinessException("订单不存在");
        }
        LocalDateTime nowTime = LocalDateTime.now();
        if (nowTime.isAfter(serviceOrder.getServiceExpDate()) || nowTime.isBefore(serviceOrder.getServiceEffDate())) {
            log.info("serviceOrder未在有效时间范围内， orderId：{}", order.getCardOrderNo());
            throw new BusinessException("订单不存在");
        }

        String orderStatus = String.valueOf(order.getStatus());
        if (OrderConstant.DEALING_ORDER_STATUS.equals(orderStatus)) {
            throw new BusinessException("订单正在处理中....");
        } else if (OrderConstant.SUCCESS_ORDER_STATUS.equals(orderStatus)) {
            throw new BusinessException("订单已完成");
        } else if (OrderConstant.EXCEPTION_ORDER_STATUS.equals(orderStatus)) {
            throw new BusinessException("订单异常!!");
        } else if (OrderConstant.CANCLE_ORDER_STATUS.equals(orderStatus)) {
            throw new BusinessException("订单不存在!");
        }

        if (StrUtil.isBlank(order.getPayWay())) {
            log.info("投放方式为空,orderId:{}", order.getOrderId());
            throw new BusinessException("当前领取渠道正在维护中，请稍后再试");
        }

        if (OrderConstant.PayWay.BANK_CARDS.equals(order.getPayWay())) {
            if (StrUtil.isBlank(sunshineReceiveCardRollsDTO.getBankCardNo())) {
                throw new BusinessException("银行卡不能为空");
            }
        }
        Integer pageType = sunshineReceiveCardRollsDTO.getPageType();
        if(pageType != null){
            if(Objects.equals(pageType, CommonConstant.PageType.ALIPAY)){
                String payWay = sunshineReceiveCardRollsDTO.getPayWay();
                if (payWay.equals(OrderConstant.PayWay.BANK_CARDS)) {
                    if (StrUtil.isBlank(sunshineReceiveCardRollsDTO.getBankCardNo())) {
                        throw new BusinessException("银行卡不能为空");
                    }
                    //修改支付方式
                    //TODO 是否还要修改
//                    changePayWay(order,serviceOrder,OrderConstant.PayWay.BANK_CARDS);
                }
            }else if(Objects.equals(pageType, CommonConstant.PageType.BANK)){
                if (order.getPayWay().equals(OrderConstant.PayWay.ALIPAY)) {
                    if (StrUtil.isBlank(sunshineReceiveCardRollsDTO.getBankCardNo())) {
                        throw new BusinessException("银行卡不能为空");
                    }
                    //修改支付方式
                    //TODO 是否还要修改
//                    changePayWay(order,serviceOrder,OrderConstant.PayWay.BANK_CARDS);
                }
            }
        }

        String lockKey = OpenApiConstant.LockKey.ORDER_LOCK + orderId;
        RLock rLock = redissonClient.getReadWriteLock(lockKey).readLock();

        String payCompany;
        // 读锁
        try {
            if (!rLock.tryLock(10, 20 * 1000 * 60, TimeUnit.MILLISECONDS)) {
                log.info("locked fail. lockKey: {}, thread: {}", lockKey, Thread.currentThread().getId());
                throw new BusinessException("系统异常");
            } else {
                order = orderService.selectOrderByApiSource(orderByApiSource);

                if (OrderConstant.PayWay.BANK_CARDS.equals(order.getPayWay())) {
                    payCompany = PaymentAisleConstant.LianlianPay.PAY_COMPANY;
                } else {
                    payCompany = PaymentAisleConstant.ALIPAY_PARY_WAY;
                }

                // 修改order订单为处理中（领取中）
                sunshineOrderManager.changeSinosigStatusToStart(order, serviceOrder, payCompany,
                        sunshineReceiveCardRollsDTO.getRedirectUrl(), sunshineReceiveCardRollsDTO.getBankCardNo());
            }
        } catch (InterruptedException e) {
            throw new BusinessException("系统异常");
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                log.info("unlocked. thread: {}", Thread.currentThread().getId());
                rLock.unlock();
            }
        }

        // 限额
        order.setPayCompany(payCompany);
        paymentService.sinosigOrganizeQuota(order, serviceOrder, null, order.getPayWay(), payCompany);

        int lockWaitTime = 30;
        //锁最大释放时间60s
        int lockLeaseTime = 60;

        //订单锁
        String key = YuJiaConstant.Client.SUNSHINE + YuJiaConstant.COLON + "payOrder" + YuJiaConstant.COLON + sunshineReceiveCardRollsDTO.getOrderType()
                + YuJiaConstant.COLON + orderId;

        if (OrderConstant.PayWay.BANK_CARDS.equals(order.getPayWay())) {
            // 投放方式（银行卡）
            order.setBankCardNo(sunshineReceiveCardRollsDTO.getBankCardNo());
            bankCardsMethod(order, serviceOrder, key, lockWaitTime, lockLeaseTime);
        } else if (OrderConstant.PayWay.ALIPAY.equals(order.getPayWay())) {
            // 投放方式（支付宝）
            //TODO 是否还要发放zfb
//            aliPayMethod(order, serviceOrder, key, lockWaitTime, lockLeaseTime);
        } else {
            throw new BusinessException("领取方式异常");
        }

        return "处理中";
    }


    /**
     * 银行卡投放方式流程
     */
    public void bankCardsMethod(Order order, ServiceOrder serviceOrder, String key, int lockWaitTime, int lockLeaseTime) {
        //记录用户操作
        userOperateInfoService.addUserOperate(order);

        String orderId = order.getOrderId();
        //创建lianlian订单
        PayOrderLianlian unfinishedLianlianOrder = payOrderLianlianService.findPaidOrPayingOrder(orderId);
        if (unfinishedLianlianOrder != null) {
            throw new BusinessException("订单处理中");
        }
        // 创建连连订单
        DLock lock = lockManager.getLock(key);
        //TODO 请确认锁用法
        PayOrderLianlian newLianLianOrder = lock.tryLock(lockWaitTime, TimeUnit.SECONDS,
                () -> sunshineOrderManager.createLianLianOrder(order), () -> {
            throw new BusinessException("wait timeout, lock key: " + key);
        });

        log.info("Sunshine receiveCardRolls orderInfo:{}", order);

        //发送连连代付请求
        paymentService.sendNewLianlianPaymentRequest(order, newLianLianOrder.getPayOrderId(), serviceOrder);
    }


}
