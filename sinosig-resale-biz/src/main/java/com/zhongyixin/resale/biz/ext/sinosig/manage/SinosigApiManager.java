package com.zhongyixin.resale.biz.ext.sinosig.manage;


import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigDaySettleDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyResponseDTO;
import com.zhongyixin.resale.biz.ext.sinosig.output.SinosigSettleResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 17:49
 */
public interface SinosigApiManager {

    SinosigOrderCashNotifyResponseDTO cashNotify(SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO);

    SinosigSettleResult settlePush(SinosigDaySettleDTO sinosigDaySettleDTO);

    void doCashNotify(Order order, String voucherId, String businessCode, String bizType);


    boolean verifyInitialSortSign(Map<String, Object> paramsMap);

    String getSign(Map<String, Object> paramsMap);



}
