package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.SysLog;
import com.zhongyixin.resale.biz.mapper.SysLogMapper;
import com.zhongyixin.resale.biz.service.SysLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {



    @Override
    public Boolean add(SysLog sysLog) {
//        sysLog.setUserName();
//        sysLog.setMethod();
//        sysLog.setParams();
//        sysLog.setIp();
//        sysLog.setCreateDate(LocalDateTime.now());
//        sysLog.setType();
//        sysLog.setModel();
//        sysLog.setResult();
//        sysLog.setDescription();
//        sysLog.setUrl();
        return save(sysLog);
    }

}
