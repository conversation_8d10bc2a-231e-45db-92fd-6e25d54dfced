package com.zhongyixin.resale.biz.ext.sinosig.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 2024/3/7 11:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SinosigOrderCreateOutput extends BaseOutput{

      private ServicePackage serverPackage;

      @Data
      public static class ServicePackage{

            @JsonProperty("card_code")
            private String cardCode;

            @JsonProperty("service_eff_date")
            private LocalDateTime serviceEffDate;

            @JsonProperty("service_exp_date")
            private LocalDateTime serviceExpDate;

            @JsonProperty("create_time")
            private LocalDateTime createTime;

      }


}
