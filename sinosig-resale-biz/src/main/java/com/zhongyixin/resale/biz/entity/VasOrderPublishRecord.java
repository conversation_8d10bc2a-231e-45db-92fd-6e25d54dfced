package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 卡券投放记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VasOrderPublishRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 投放时间
     */
    private LocalDateTime pushTime;

    /**
     * 投放成功时间
     */
    private LocalDateTime pushSuccessTime;

    /**
     * 删除标识;删除逻辑标识（1：删除;  0：不删除）
     */
    private Boolean deleted;

    /**
     * 重试人ID
     */
    private Long retryUserId;

    /**
     * 重试人名字
     */
    private String retryUserName;

    /**
     * 外部单号
     */
    private String externalOrderNo;

    /**
     * 主单号
     */
    private String orderId;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 子单号
     */
    private String childOrderNo;

    /**
     * 订单类型（1、银行卡；2、支付宝）
     */
    private Integer orderType;

    /**
     * 电话
     */
    private String tel;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 名字
     */
    private String userName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 状态（0.待处理；1、处理中；2、成功；3、失败；4、异常）
     */
    private Integer status;


    /**
     * 三方批次卡卷编号（支付宝是activity_id,微信是卡卷的批次stock_id）
     */
    private String extVoucherId;

    /**
     * 三方活动id
     */
    private String extActivityId;

    /**
     * 备注
     */
    private String remark;


}
