package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.OrderWriteOff;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单核销表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface OrderWriteOffMapper extends BaseMapper<OrderWriteOff> {

    OrderWriteOff selectOneByOrderId(@Param("orderId") String orderId);

    int updateVasCouponStatus(@Param("orderWriteOff") OrderWriteOff orderWriteOff, @Param("originalStatusList") List<Integer> originalStatusList);

}
