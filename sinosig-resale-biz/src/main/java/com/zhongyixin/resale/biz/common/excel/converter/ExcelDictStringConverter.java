package com.zhongyixin.resale.biz.common.excel.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.zhongyixin.resale.biz.common.annotation.ExcelConverterTyep;
import com.zhongyixin.resale.biz.common.enums.ExcelStateConverter;

import java.lang.reflect.Field;


public class ExcelDictStringConverter implements Converter<String> {



    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

        Field field = contentProperty.getField();
        if(null==value) {
            return new WriteCellData<>();
        }
        String type = field.getAnnotation(ExcelConverterTyep.class).type();

        return new WriteCellData<>(String.valueOf(ExcelStateConverter.getValue(type, Integer.parseInt(value))));
    }


}
