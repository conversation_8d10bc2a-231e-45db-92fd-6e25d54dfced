package com.zhongyixin.resale.biz.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * <p>
 * 阳光回调记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07 10:43:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SinosigNotifyRecord {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务订单号
     */
    private String orderId;

    /**
     * 回调参数
     */
    private String notifyParams;

    /**
     * 返回参数
     */
    private String responseParams;

    /**
     * 异常信息
     */
    private String exceptionMessage;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer deleted;


}
