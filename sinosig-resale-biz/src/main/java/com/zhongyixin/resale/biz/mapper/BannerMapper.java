package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.Banner;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
public interface BannerMapper extends BaseMapper<Banner> {

    List<Banner> selectListByType(@Param("status") Integer status,@Param("apiSource")String apiSource,@Param("type")Integer type);

    List<Banner> selectListByTypes(@Param("status") Integer status,@Param("apiSource")String apiSource,@Param("type")List<Integer> type);

    List<Banner> selectListByStatus(@Param("status") Integer status,@Param("apiSource")String apiSource);

}
