package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.constant.SinosigDtoConvert;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.entity.SinosigOrderInfo;
import com.zhongyixin.resale.biz.mapper.SinosigOrderInfoMapper;
import com.zhongyixin.resale.biz.service.SinosigOrderInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 阳光订单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
@Slf4j
public class SinosigOrderInfoServiceImpl extends ServiceImpl<SinosigOrderInfoMapper, SinosigOrderInfo> implements SinosigOrderInfoService {

    @Resource
    SinosigOrderInfoMapper sinosigOrderInfoMapper;

    @Override
    public void updateRepayInfoByRepayBatchNo(String repayBatchNo, String repayId, LocalDate endRateDate) {
        Integer count = sinosigOrderInfoMapper.selectOrderIdsCount(repayBatchNo);
        if(count == 0){
            return ;
        }
        // 每个批次查询的数据数量
        int size = 5000;
        int offset = 0;
        while (offset < count){
            List<String> sinosigOrderIds = sinosigOrderInfoMapper.selectSinosigOrderIds(repayBatchNo, offset, size);
            sinosigOrderInfoMapper.updateRepayInfoBySinosigOrderId(sinosigOrderIds,repayId,endRateDate);
            offset += size;
        }

    }

    @Override
    public void createSinosigOrderInfo(ServiceOrder serviceOrder, String orderId) {
        SinosigOrderInfo sinosigOrderInfo = SinosigDtoConvert.INSTANCE.toSinosigOrderInfo(serviceOrder);
        sinosigOrderInfo.setOrderId(orderId);
        SinosigOrderInfo sinosigOrderInfoInDB = sinosigOrderInfoMapper.selectByOrderId(sinosigOrderInfo.getSinosigOrderId());
        if(sinosigOrderInfoInDB == null){
            save(sinosigOrderInfo);
            log.info("sinosigOrderInfo saved. sinosigOrderId is {}",sinosigOrderInfo.getSinosigOrderId());
        }else {
            log.info("sinosigOrderInfo exist. sinosigOrderId is {}",sinosigOrderInfo.getSinosigOrderId());
        }
    }

    @Override
    public Integer updateStartRateInfoBySinosigOrderId(String sinosigOrderId, LocalDate startRateDate) {
        return sinosigOrderInfoMapper.updateStartRateInfoBySinosigOrderId(sinosigOrderId,startRateDate);
    }


}
