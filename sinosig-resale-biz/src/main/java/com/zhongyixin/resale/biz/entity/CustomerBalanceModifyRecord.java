package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户余额变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_customer_balance_modify_record")
public class CustomerBalanceModifyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构编码
     */
    private String code;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 变更类型
     */
    private String type;

    /**
     * 变更金额
     */
    private BigDecimal amount;

    /**
     * 变更前余额
     */
    private BigDecimal modifyBeforeAmount;

    /**
     * 当前余额
     */
    private BigDecimal currentAmount;

    /**
     * 支付商编
     */
    private String paymentNo;

    /**
     * 代付通道
     */
    private String payCompany;


}
