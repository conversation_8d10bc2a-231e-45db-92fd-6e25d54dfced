package com.zhongyixin.resale.biz.common.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wftk.exception.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author:lzp
 * @create: 2022-08-02 14:05
 * @Description: 导入数据通用监听器
 */

@Slf4j
public class MyExcelListener<T> extends AnalysisEventListener<T> {

    private Map<String, Object> param;

    /**
     * 500条保存一次数据
     */
    private static final int BATCH_COUNT = 1000;

    /**
     * 数据缓存
     */
    private List<T> list = new ArrayList<>(BATCH_COUNT);

    private SaveInterface<T> saveInterface;

    public MyExcelListener(SaveInterface<T> saveInterface, Map<String, Object> param) {
        this.saveInterface = saveInterface;
        this.param = param;
    }

    public MyExcelListener(SaveInterface<T> saveInterface) {
        this.saveInterface = saveInterface;
    }

    public MyExcelListener() {
    }

    @Override
    public void invoke(T data, AnalysisContext analysisContext) {
        if (isLineNullValue(data)) {
            return;
        }

        try {
            //通用方法数据校验
            ExcelImportValid.valid(data);
        } catch (Exception e) {
            //在easyExcel监听器中抛出业务异常
            throw new BusinessException(e.getMessage());
        }

        //先将数据加到list中
        list.add(data);

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (list.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            list = new ArrayList<>(BATCH_COUNT);
        }
    }

    /**
     * 最后再存储一次数据
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
    }

    private boolean isLineNullValue(T data) {
        if (data instanceof String) {
            return StringUtils.isBlank((String)data);
        }
        try {
            List<Field> fields = Arrays.stream(data.getClass().getDeclaredFields())
                    .filter(f -> f.isAnnotationPresent(ExcelProperty.class))
                    .collect(Collectors.toList());
            List<Boolean> lineNullList = new ArrayList<>(fields.size());
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(data);
                if (Objects.isNull(value)) {
                    lineNullList.add(Boolean.TRUE);
                } else {
                    lineNullList.add(Boolean.FALSE);
                }
            }
            return lineNullList.stream().allMatch(Boolean.TRUE::equals);
        } catch (Exception e) {
            log.error("读取数据行[{}]解析失败", data, e);
        }
        return true;
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", list.size());
        if(!list.isEmpty()){
            saveInterface.save(list,param);
        }
        log.info("存储数据库成功！");
    }

}
