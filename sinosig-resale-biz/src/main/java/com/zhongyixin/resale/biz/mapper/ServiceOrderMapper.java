package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhongyixin.resale.biz.common.annotation.DataPermission;
import com.zhongyixin.resale.biz.common.annotation.DataScopeType;
import com.zhongyixin.resale.biz.common.enums.DataScope;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.*;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.entity.SinosigDaySettleTask;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 阳光明细订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface ServiceOrderMapper extends BaseMapper<ServiceOrder> {

   /**
    * 查询订单明细信息
    * @param serviceOrderSearchDTO
    * @param page
    * @return
    */
   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   IPage<ServiceOrderInfoVO> selectServiceOrderInfoVO(IPage<ServiceOrder> page, @Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO);

   /**
    * 根据订单号或者手机号查询订单
    * @param orderId
    * @param phone
    * @return
    */
   IPage<SunshineOrderStatusDTO> selectServiceOrderByOrderIdAndPhone(IPage<SunshineOrderStatusDTO> page, @Param("orderId")String orderId, @Param("phone")String phone,
                                                                     @Param("payCompany") String payCompany);

   /**
    * 统计订单明细信息总数量/总金额/总含税金额/总利息
    * @param serviceOrderSearchDTO
    * @return
    */
   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   ServiceOrderStatisticsDataVO statisticsServiceOrderTotalData(@Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO);

   /**
    * 统计订单明细信息总数量
    * @param serviceOrderSearchDTO
    * @return
    */
   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   Integer statisticsServiceOrderTotalCount(@Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO);

   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   List<ServiceOrderInfoVO> selectServiceOrderInfoVOList(@Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO,
                                                         @Param("offset") Long offset, @Param("batchSize") Long batchSize);

   /**
    * 根据父级机构编码、垫资状态获取订单金额
    * @param parentOrgCode
    * @param repayStatus
    * @return
    */
   BigDecimal getAmountByOrg(@Param("parentOrgCode") String parentOrgCode, @Param("repayStatus")Integer repayStatus);

   /**
    * 根据父级机构编码获取订单金额
    * @param parentOrgCode
    * @return
    */
   List<BigDecimal> getAmountByOrgCode(@Param("parentOrgCode") String parentOrgCode);

   /**
    * 根据订单号查询copy阳光订单
    * @param list
    * @return
    */
   List<ServiceOrderCopyDTO> listCopyOrder(List<String> list);

   /**
    * 查询订单
    * @param orderId
    * @return
    */
   ServiceOrderCopyDTO selectServiceOrderCopyDTOByOrderId(String orderId);

   /**
    * 根据父级机构编码获取订单金额
    * @param parentOrgCode
    * @return
    */
   BigDecimal getAmountByOrgParentCode(@Param("parentOrgCode") String parentOrgCode);

   /**
    * 查询订单
    * @param orderId
    * @return
    */
   ServiceOrder selectServiceOrderByOrderId(String orderId);

   /**
    * 修改订单状态
    * @param serviceOrder
    * @return
    */
   Integer updateServiceOrderRepayStatus(ServiceOrder serviceOrder);

   /**
    * 批量修改Ip
    * @param serviceOrders
    * @return
    */
   Integer updateBatchInsIpByOrderIds(List<ServiceOrder> serviceOrders);

   /**
    * 根据父级机构编码、垫资状态获取超过订单周期的订单数目
    * @param parentOrgCode
    * @param repayStatus
    * @param tranDate
    * @return
    */
   int getOrderNumbserByOrg(@Param("parentOrgCode") String parentOrgCode, @Param("repayStatus")Integer repayStatus, @Param("tranDate") LocalDate tranDate);

   /**
    * 修改未垫资订单信息
    * @param serviceOrder
    * @return
    */
   int updateOrderInfoByOrderId(ServiceOrder serviceOrder);

   /**
    * 根据批次连表修改阳光还款信息
    * @param serviceOrder
    * @param repayBatchNo
    * @return
    */
   int updateOrderRepayInfo(@Param("serviceOrder")ServiceOrder serviceOrder,@Param("repayBatchNo")String repayBatchNo);

   /**
    * 计算未还款订单金额
    * @param repayBatchNo
    * @return
    */
   BigDecimal calcNonRepayOrderAmount(String repayBatchNo);

   /**
    * 根据文件摘要查询还款订单信息
    * @param fileSign
    * @return
    */
   List<SunshineRepayMoneyAndTotalVO> getAmountAndTotalByOrderIds(@Param("fileSign") String fileSign);


   /**
    * 根据文件摘要查询还款订单数量
    * @param fileSign
    * @return
    */
   Integer getTotalByOrderIds(@Param("fileSign") String fileSign);

   List<String> getOrgCode(@Param("fileSign") String fileSign);

   /**
    * 根据文件摘要查询含税总金额
    * @param fileSign
    * @return
    */
   BigDecimal getTaxTotalAmount(@Param("fileSign") String fileSign);

   Integer updateOrder(UpdateOrderDTO dto);

   Integer cancelOrder(String orderId);


   AmountAndCountVO dailyOrderStatistics(AmountAndCountDTO dto);


   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   IPage<ServiceOrderInfoVOV2> selectServiceOrderInfoVOV2(IPage<ServiceOrder> page, @Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO);


   /**
    * 统计订单明细信息总数量/总金额/总含税金额/总利息
    * @param serviceOrderSearchDTO
    * @return
    */
   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   ServiceOrderStatisticsDataVO statisticsServiceOrderTotalDataV2(@Param("serviceOrderSearchDTO") ServiceOrderSearchDTO serviceOrderSearchDTO);

   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   IPage<AllocationServiceOrderDto> selectAllocationSecondOrder(IPage<ServiceOrder> page, @Param("orgParentCode")String orgParentCode,
                                                                @Param("orderMonth") String orderMonth, @Param("orderStartTime") LocalDate orderStartTime,
                                                                @Param("orderEndTime")LocalDate orderEndTime);

   @DataPermission(dataScopes = {
           @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ),
           @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
   })
   IPage<AllocationServiceOrderDto> selectAllocationThirdOrder(IPage<ServiceOrder> page, @Param("orgCode")String orgCode,@Param("orgParentCode")String orgParentCode,
                                                               @Param("orderMonth") String orderMonth, @Param("orderStartTime") LocalDate orderStartTime,
                                                               @Param("orderEndTime")LocalDate orderEndTime);


   List<ServiceOrderCopyDTO> selectAllocationWaitOrder(@Param("orgParentCode") String orgParentCode, @Param("orgCode") String orgCode);

   List<ServiceOrderCopyDTO> selectWaitPushOrder(@Param("startTime")LocalDate startTime, @Param("endTime")LocalDate endTime, @Param("orderMode")Integer orderMode);


   List<ServiceOrder> selectListByProvider(@Param("paytime")LocalDate paytime,@Param("provider")String provider);

   Integer deferredServiceOrder(@Param("orderId")String orderId, @Param("serviceExpDate")LocalDateTime serviceExpDate);

}
