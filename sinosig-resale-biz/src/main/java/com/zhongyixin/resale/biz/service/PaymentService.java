package com.zhongyixin.resale.biz.service;


import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.entity.ServiceOrder;

import java.time.LocalDateTime;

/**
 * <p>
 * 支付服务
 * </p>
 * <AUTHOR>
 * @since 2023-02-09
 */
public interface PaymentService {



    /**
     * 发送连连支付请求
     * @param order
     * @param payOrderId PaymentRequest
     * @param serviceOrder
     */
    void sendLianlianPaymentRequest(Order order , String payOrderId, ServiceOrder serviceOrder);

    /**
     * 发送连连支付请求
     * @param order
     * @param payOrderId PaymentRequest
     * @param serviceOrder
     */
    void sendNewLianlianPaymentRequest(Order order , String payOrderId, ServiceOrder serviceOrder);

    /**
     * 发送连连支付请求(不包含阳光)
     * @param order
     * @param payOrderId PaymentRequest
     */
    void sendLianlianPaymentRequest(Order order , String payOrderId);



    /**
     * 支付成功处理逻辑
     * @param order
     * @param  dateTime
     * @return
     */
    void paymentSuccessDealLogic(Order order, LocalDateTime dateTime);

    /**
     * 连连代付查询
     * @param payOrderLianlian
     * @return
     */
    void orderQueryLianlianPayApi(PayOrderLianlian payOrderLianlian);


    void publishOrderXConsumeSuccessEvent(Order order,String orderId);


    /**
     * 阳光机构限额
     */
    void sinosigOrganizeQuota(Order order,ServiceOrder serviceOrder,String payOrderId,String payWay,String payCompany);


}
