package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.common.input.DataScopeDTO;
import com.zhongyixin.resale.biz.entity.SysDept;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 系统部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 获取当前用户下的权限
     * @param userId
     * @return
     */
    DataScopeDTO selectDataPermissionByUserId(Integer userId);

    SysDept findDeptByDeptName(@Param("deptName") String deptName);

    String findSecondOrgAncestors(@Param("orgCode") String orgCode);

    String findSecondOrgName(@Param("orgCode") String orgCode);

}
