package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.Issue;
import com.zhongyixin.resale.biz.mapper.IssueMapper;
import com.zhongyixin.resale.biz.service.IssueService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class IssueServiceImpl extends ServiceImpl<IssueMapper, Issue> implements IssueService {

    @Resource
    IssueMapper issueMapper;

    /**
     * 根据客户来源查询发布中的发布详情
     *
     * @param apiSource
     * @param orderType
     * @return
     */
    @Override
    public Issue selectOneByApiSource(String apiSource, String orderType) {
        return issueMapper.selectOneByApiSource(apiSource, orderType);
    }
}
