package com.zhongyixin.resale.biz.common.input;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhongyixin.resale.biz.common.excel.ExcelValid;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
public class SunshineReapyOrderDTO {

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    @ExcelValid(message = "订单号不能为空")
    private String ordeId;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;

    /**
     * 摘要
     */
    @ExcelIgnore
    private String fileSign;

    /**
     * 备注
     */
    @ExcelIgnore
    private BigDecimal amount;

    /**
     * 摘要
     */
    @ExcelIgnore
    private Integer total;

}
