package com.zhongyixin.resale.biz.common.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @createDate 2024/5/14 15:44
 */
@Data
public class OrganizePreserveStatusDTO {

    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    @NotNull(message = "维护状态不能为空")
    private Integer preserveStatus;

}
