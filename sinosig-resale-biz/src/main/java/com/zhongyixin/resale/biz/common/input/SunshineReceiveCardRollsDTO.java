package com.zhongyixin.resale.biz.common.input;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR>
 * @create 2024/3/7 18:25
 */
@Data
public class SunshineReceiveCardRollsDTO {

    /**
     * 用户手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String telephone;

    /**
     * 卡密
     */
    @NotBlank(message = "卡密不能为空")
    private String cardCode;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    /**
     * 金额
     */
    @NotBlank(message = "金额不能为空")
    private String balance;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 重定向路径
     */
    private String redirectUrl;

    /**
     * 路径跳转类型 (订单类型)
     */
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    /**
     * 客户来源
     */
    @NotBlank(message = "客户来源不能为空")
    private String apiSource;

    /**
     * 页面类型 1.银行卡  2.支付宝
     */
    private Integer pageType;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String payWay;

}
