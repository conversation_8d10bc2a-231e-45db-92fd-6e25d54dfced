package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.zhongyixin.resale.biz.common.constant.NotifyJobConstant;
import com.zhongyixin.resale.biz.common.constant.OrderConstant;
import com.zhongyixin.resale.biz.common.constant.OrderWriteOffConstant;
import com.zhongyixin.resale.biz.entity.NotifyJob;
import com.zhongyixin.resale.biz.entity.OrderWriteOff;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.ext.sinosig.manage.SinosigApiManager;
import com.zhongyixin.resale.biz.mapper.OrderWriteOffMapper;
import com.zhongyixin.resale.biz.service.OrderService;
import com.zhongyixin.resale.biz.service.OrderWriteOffService;
import com.zhongyixin.resale.biz.service.NotifyJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单核销表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
public class OrderWriteOffServiceImpl extends ServiceImpl<OrderWriteOffMapper, OrderWriteOff> implements OrderWriteOffService {

    @Autowired
    private OrderWriteOffMapper orderWriteOffMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private NotifyJobService notifyJobService;

    @Autowired
    private SinosigApiManager sinosigApiManager;


    @Autowired
    private HttpRequestExecutor requestExecutor;




    @Override
    public OrderWriteOff selectOneByOrderId(String orderId) {
        return orderWriteOffMapper.selectOneByOrderId(orderId);
    }

    @Override
    public void updateOldOrderWriteOff() {
        List<Order> orders = orderService.getOrderListByStatus(Integer.valueOf(OrderConstant.SUCCESS_ORDER_STATUS));
        for(Order order : orders){
            OrderWriteOff writeOff = orderWriteOffMapper.selectOneByOrderId(order.getOrderId());
            if(writeOff != null){
                continue;
            }

            boolean needSave = false;
            Integer orderStatus = null;
            Integer vasCouponStatus = null;
            String remark = null;
            LocalDateTime updateTime = null;
            //查询job
            NotifyJob cashNotifyJob = notifyJobService.queryJobByCodeAndOrderId(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY, order.getOrderId());
            NotifyJob couponNotifyJob = notifyJobService.queryJobByCodeAndOrderId(NotifyJobConstant.BusinessCode.SINOSIG_COUPON_CASH_NOTIFY, order.getOrderId());
            if(cashNotifyJob != null){
                needSave = true;
                Integer status = cashNotifyJob.getStatus();
                if(Objects.equals(status, NotifyJobConstant.Status.SUCCESS)){
                    orderStatus = OrderWriteOffConstant.OrderStatus.SUCCESS;
                }else if(Objects.equals(status,NotifyJobConstant.Status.FAIL)){
                    orderStatus = OrderWriteOffConstant.OrderStatus.FAIL;
                }else if(Objects.equals(status,NotifyJobConstant.Status.WAIT)){
                    orderStatus = OrderWriteOffConstant.OrderStatus.WAIT;
                }
                remark = cashNotifyJob.getRemark();
                updateTime = cashNotifyJob.getUpdateTime();
            }
            if(couponNotifyJob != null){
                needSave = true;
                Integer status = cashNotifyJob.getStatus();
                if(Objects.equals(status,NotifyJobConstant.Status.SUCCESS)){
                    vasCouponStatus = OrderWriteOffConstant.VasCouponStatus.SUCCESS;
                }else if(Objects.equals(status,NotifyJobConstant.Status.FAIL)){
                    vasCouponStatus = OrderWriteOffConstant.VasCouponStatus.FAIL;
                }else if(Objects.equals(status,NotifyJobConstant.Status.WAIT)){
                    vasCouponStatus = OrderWriteOffConstant.VasCouponStatus.WAIT;
                }
                remark = couponNotifyJob.getRemark();
            }

            if(needSave){
                OrderWriteOff orderWriteOff = new OrderWriteOff();
                orderWriteOff.setOrderId(order.getOrderId());
                orderWriteOff.setSinosigOrderId(order.getCardOrderNo());
                orderWriteOff.setCreateTime(order.getCreateTime());
                if(vasCouponStatus != null){
                    orderWriteOff.setVasCouponStatus(vasCouponStatus);
                }
                if(orderStatus != null){
                    orderWriteOff.setOrderStatus(orderStatus);
                }
                if(StrUtil.isNotBlank(remark)){
                    orderWriteOff.setRemark(remark);
                }
                if(updateTime != null){
                    orderWriteOff.setUpdateTime(updateTime);
                }
                save(orderWriteOff);
            }
        }
    }

    @Override
    public void retry(String orderId) {
        OrderWriteOff orderWriteOff = selectOneByOrderId(orderId);
        if(orderWriteOff == null){
            throw new BusinessException("核销数据不存在！");
        }
        Integer orderStatus = orderWriteOff.getOrderStatus();
        Integer vasCouponStatus = orderWriteOff.getVasCouponStatus();

        if(Objects.equals(orderStatus, OrderWriteOffConstant.OrderStatus.SUCCESS) &&
                Objects.equals(vasCouponStatus,OrderWriteOffConstant.VasCouponStatus.SUCCESS)){
            throw new BusinessException("该笔订单已经核销通知成功，请勿重复发起核销通知");
        }

        if(OrderWriteOffConstant.VasCouponStatus.SUCCESS.equals(orderStatus)){
            //修改子卡券核销状态为核销中
            orderWriteOff.setVasCouponStatus(OrderWriteOffConstant.VasCouponStatus.PROCESS);
            List<Integer> originalStatusList = new ArrayList<>();
            originalStatusList.add(OrderWriteOffConstant.VasCouponStatus.FAIL);
            originalStatusList.add(OrderWriteOffConstant.VasCouponStatus.WAIT);
            boolean b = updateVasCouponStatus(orderWriteOff, originalStatusList);
            if(b){
                //主订单核销成功，调用付卡券核销
                NotifyJob notifyJob = notifyJobService.queryJobByCodeAndOrderId(NotifyJobConstant.BusinessCode.SINOSIG_COUPON_CASH_NOTIFY, orderId);
                if(notifyJob != null){
//                    requestExecutor.execute(notifyJob);
                }

            }

        }else if(OrderWriteOffConstant.VasCouponStatus.FAIL.equals(orderStatus) || OrderWriteOffConstant.VasCouponStatus.UNEXPECTED.equals(orderStatus)){
            //失败，
            NotifyJob notifyJob = notifyJobService.queryJobByCodeAndOrderId(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY, orderId);
            if(notifyJob != null){
//                requestExecutor.execute(notifyJob);
            }


        }

        //查询使用核销，核销失败或者未核销需要重新发起
        OrderWriteOff newOrderWriteOff = selectOneByOrderId(orderId);
        if(Objects.equals(newOrderWriteOff.getVasCouponStatus(), OrderWriteOffConstant.VasCouponStatus.SUCCESS)){
            NotifyJob notifyJob = notifyJobService.queryJobByCodeAndOrderId(NotifyJobConstant.BusinessCode.SINOSIG_CASH_NOTIFY, orderId);
            if(!Objects.equals(notifyJob.getStatus(), NotifyJobConstant.Status.SUCCESS)){
//                requestExecutor.execute(notifyJob);
            }

        }


    }

    @Override
    public boolean updateVasCouponStatus(OrderWriteOff orderWriteOff, List<Integer> originalStatusList) {
        int i = baseMapper.updateVasCouponStatus(orderWriteOff, originalStatusList);
        return i > 0;
    }
}
