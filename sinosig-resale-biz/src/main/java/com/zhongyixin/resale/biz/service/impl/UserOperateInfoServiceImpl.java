package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.constant.CommonDtoToVoConvert;
import com.zhongyixin.resale.biz.common.util.AddressUtils;
import com.zhongyixin.resale.biz.common.util.IpUtils;
import com.zhongyixin.resale.biz.common.util.ServletUtils;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.UserOperateInfo;
import com.zhongyixin.resale.biz.mapper.UserOperateInfoMapper;
import com.zhongyixin.resale.biz.service.UserOperateInfoService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户操作记录表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
public class UserOperateInfoServiceImpl extends ServiceImpl<UserOperateInfoMapper, UserOperateInfo> implements UserOperateInfoService {

    /**
     * 记录订单的用户操作信息
     * @param order ip
     */
    @Override
    public void addUserOperate(Order order) {
        UserOperateInfo userOperateInfo = CommonDtoToVoConvert.INSTANCE.orderToUserOperateInfo(order);
        userOperateInfo.setIpAddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        userOperateInfo.setDevice(ServletUtils.getRequest().getHeader("User-Agent"));
        userOperateInfo.setLocation(AddressUtils.getRealAddressByIp(userOperateInfo.getIpAddr()));
        this.save(userOperateInfo);
    }
}
