package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 还款明细清单对象 tb_repayment_inventory
 *
 * @date 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_sunshine_repayment_file_data")
public class SunshineRepaymentFileData implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 文件摘要
     */
    private String fileSign;
    /**
     * 上传人
     */
    private String uploadBy;
    /**
     * 阳光订单号
     */
    private String sunshineOrderId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
