package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.pageable.spring.boot.autoconfigure.core.Page;
import com.zhongyixin.resale.biz.common.constant.ChannelSettingConstant;
import com.zhongyixin.resale.biz.entity.ChannelSetting;
import com.zhongyixin.resale.biz.mapper.ChannelSettingMapper;
import com.zhongyixin.resale.biz.service.ChannelSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 渠道通道配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Service
public class ChannelSettingServiceImpl extends ServiceImpl<ChannelSettingMapper, ChannelSetting> implements ChannelSettingService {

    @Autowired
    ChannelSettingMapper channelSettingMapper;


    @Override
    public ChannelSetting selectOneByChannelCode(String channelCode) {
        return channelSettingMapper.selectOneByChannelCode(channelCode);
    }

}
