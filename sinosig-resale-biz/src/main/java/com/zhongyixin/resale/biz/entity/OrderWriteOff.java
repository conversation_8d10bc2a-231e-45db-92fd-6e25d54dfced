package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单核销表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderWriteOff implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    private Integer deleted;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0.待核销  1.核销中  2.核销成功  3.核销失败）
     */
    private Integer orderStatus;

    /**
     * 状态（0.待核销  1.核销中  2.核销成功  3.核销失败）
     */
    private Integer vasCouponStatus;

    /**
     * 业务订单号
     */
    private String orderId;

    /**
     * 阳光订单号
     */
    private String sinosigOrderId;


}
