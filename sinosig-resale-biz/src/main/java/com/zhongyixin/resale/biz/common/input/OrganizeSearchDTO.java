package com.zhongyixin.resale.biz.common.input;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizeSearchDTO extends PaginationDTO{

    /**
     * 状态
     */
    private Integer status;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 维护状态（0正常 1维护中）
     */
    private Integer preserveStatus;

    /**
     * 创建起始时间
     */
    private LocalDateTime createStartDate;

    /**
     * 创建结束时间
     */
    private LocalDateTime createEndDate;

    private Boolean isAesByMaxAccountDays;

}
