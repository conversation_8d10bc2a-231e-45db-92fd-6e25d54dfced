package com.zhongyixin.resale.biz.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/10 21:58
 */
@Getter
public enum ScheduledEnum {


    /**
     * 梯度配置
     */
    def(-1,30),
    one(1,60),
    two(2,5*60),
    three(3,10*60),
    four(4,30*60);


    private final int count;
    private final int second;

    ScheduledEnum(int count, int second) {
        this.count = count;
        this.second = second;
    }

    public static int getSecond(int count) {
        for (ScheduledEnum value : values()) {
            if(value.count == count){
                return value.second;
            }
        }
        return 0;
    }
}
