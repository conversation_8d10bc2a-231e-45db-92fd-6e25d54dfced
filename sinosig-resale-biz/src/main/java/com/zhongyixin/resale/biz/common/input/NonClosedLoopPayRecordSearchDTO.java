package com.zhongyixin.resale.biz.common.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NonClosedLoopPayRecordSearchDTO extends PaginationDTO {

    @Schema(description= "手机号", example = "18273431026")
    private String telephone;

    @Schema(description= "姓名", example = "谢")
    private String userName;

    @Schema(description= "客户渠道", example = "GUOREN")
    private String apiSource;

    @Schema(description= "车牌号", example = "宁CKQ132")
    private String plateNum;

    @Schema(description= "批次密钥", example = "r4ov207t5ysaknhu6m")
    private String couponNo;

//    private String insurancestr;
}
