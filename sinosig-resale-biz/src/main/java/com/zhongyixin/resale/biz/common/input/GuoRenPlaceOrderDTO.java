package com.zhongyixin.resale.biz.common.input;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


/**
 * <AUTHOR>
 * @create 2023/2/9 17:30
 */
@Data
public class GuoRenPlaceOrderDTO {

    /**
     * 交易号
     */
    @NotBlank(message = "交易号不能为空")
    private String tradeNo;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    private String userName;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空")
    private String bankCardNo;

    /**
     * 用户手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$|", message = "手机号格式不正确")
    private String telephone;

    /**
     * 可用积分
     */
    @NotBlank(message = "积分不能为空")
    private String point;

    /**
     * 用户代付金额
     */
    @NotBlank(message = "兑换金额不能为空")
    private String realValue;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空")
    private String platformCode;

}
