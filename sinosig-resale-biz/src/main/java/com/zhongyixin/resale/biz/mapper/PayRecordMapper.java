package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhongyixin.resale.biz.common.annotation.DataPermission;
import com.zhongyixin.resale.biz.common.annotation.DataScopeType;
import com.zhongyixin.resale.biz.common.enums.DataScope;
import com.zhongyixin.resale.biz.common.input.NonClosedLoopPayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.input.SunshinePayRecordSearchDTO;
import com.zhongyixin.resale.biz.common.output.NonClosedLoopPayRecordVo;
import com.zhongyixin.resale.biz.common.output.SunshinePayRecordVo;
import com.zhongyixin.resale.biz.entity.PayRecord;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 支付记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface PayRecordMapper extends BaseMapper<PayRecord> {

    /**
     * 查询阳光支付集合列表
     * @param page
     * @param sunshinePayRecordSearchDTO
     * @return
     */
    @DataPermission(dataScopes = {
            @DataScopeType(dataScope = DataScope.PROVIDER_DATA, field = "provider_name" ,alias = "tso"),
            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code",alias = "tso" )
    })
    IPage<SunshinePayRecordVo> selectSunshinePayRecordList(IPage<PayRecord> page , @Param("sunshinePayRecordSearchDTO") SunshinePayRecordSearchDTO sunshinePayRecordSearchDTO);

    /**
     * 查询非闭环支付集合列表
     * @param page
     * @param nonClosedLoopPayRecordSearchDTO
     * @return
     */
    IPage<NonClosedLoopPayRecordVo> selectNonClosedLoopPayRecordList(IPage<PayRecord> page , @Param("nonClosedLoopPayRecordSearchDTO") NonClosedLoopPayRecordSearchDTO nonClosedLoopPayRecordSearchDTO);

    PayRecord selectLastTimeOne(@Param("orderId")String orderId,@Param("status")Integer status);

}
