package com.zhongyixin.resale.biz.common.event;

import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @createDate 2024/3/7 9:39
 */
@Getter
public class OrderCreateSucceeEvent extends ApplicationEvent {

    private Order order;

    private ServiceOrder serviceOrder;

    private String orgName;

    public OrderCreateSucceeEvent(Object source) {
        super(source);
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public void setServiceOrder(ServiceOrder serviceOrder) {
        this.serviceOrder = serviceOrder;
    }


    public OrderCreateSucceeEvent(Order order, ServiceOrder serviceOrder, String orgName) {
        super(order);
        this.order = order;
        this.serviceOrder = serviceOrder;
        this.orgName = orgName;
    }
}
