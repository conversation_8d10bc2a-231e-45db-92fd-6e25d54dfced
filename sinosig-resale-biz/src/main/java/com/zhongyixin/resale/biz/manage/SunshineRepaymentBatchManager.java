//package com.zhongyixin.resale.biz.manage;
//
//import cn.hutool.core.util.IdUtil;
//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.read.listener.PageReadListener;
//import com.alibaba.excel.util.StringUtils;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.wftk.exception.core.exception.BusinessException;
//import com.zhongyixin.resale.biz.common.enums.RepayBatchFileAssociationStatusEnum;
//import com.zhongyixin.resale.biz.common.enums.RepayBatchStatusEnum;
//import com.zhongyixin.resale.biz.common.input.SunshineReapyOrderDTO;
//import com.zhongyixin.resale.biz.common.output.SunshineRepayMoneyAndTotalVO;
//import com.zhongyixin.resale.biz.common.redis.RedisCache;
//import com.zhongyixin.resale.biz.entity.OrganizeYgry;
//import com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch;
//import com.zhongyixin.resale.biz.entity.SunshineRepaymentDetail;
//import com.zhongyixin.resale.biz.entity.SysDictData;
//import com.zhongyixin.resale.biz.ext.crm.manage.CrmApiManager;
//import com.zhongyixin.resale.biz.ext.crm.manage.input.SunshineRepayBatchInput;
//import com.zhongyixin.resale.biz.ext.crm.manage.output.SunshineRepayBatchOutput;
//import com.zhongyixin.resale.biz.mapper.OrganizeYgryMapper;
//import com.zhongyixin.resale.biz.mapper.ServiceOrderMapper;
//import com.zhongyixin.resale.biz.mapper.SunshineRepaymentBatchMapper;
//import com.zhongyixin.resale.biz.service.IOssService;
//import com.zhongyixin.resale.biz.service.ISunshineRepaymentDetailService;
//import com.zhongyixin.resale.biz.service.ISunshineRepaymentFileDataService;
//import com.zhongyixin.resale.biz.service.ISysDictDataService;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.File;
//import java.math.BigDecimal;
//import java.nio.file.Files;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * <AUTHOR>
// * @createDate 2024/5/8 19:57
// */
//@Component
//@Slf4j
//public class SunshineRepaymentBatchManager {
//
//    /**
//     * 从yml文件中读取
//     */
//    @Value("${config.filePath}")
//    private String path;
//
//    @Resource
//    IOssService ossService;
//
//    @Resource
//    SunshineRepaymentBatchMapper sunshineRepaymentBatchMapper;
//
//    @Resource
//    ISunshineRepaymentDetailService sunshineRepaymentDetailService;
//
//    @Resource
//    ISysDictDataService sysDictDataService;
//
//    @Resource
//    ServiceOrderMapper serviceOrderMapper;
//
//    @Resource
//    OrganizeYgryMapper organizeYgryMapper;
//
//    @Resource
//    RedisCache redisCache;
//
//    @Resource
//    CrmApiManager crmApiManager;
//
//    @Resource
//    ISunshineRepaymentFileDataService sunshineRepaymentFileDataService;
//
//    /**
//     * 异步调用crm流水还款接口
//     *
//     * @param sunshineRepaymentBatch
//     */
//    @Async("asyncServiceExecutor")
//    public void asyncTranCrmRepayBatch(SunshineRepaymentBatch sunshineRepaymentBatch) {
//        String downloadLink = ossService.generateDownloadLink(sunshineRepaymentBatch.getOssPath(), 600);
//        log.info("crm流水下载链接生成成功,downloadLink: {}", downloadLink);
//
//        String repayBatchNo = sunshineRepaymentBatch.getRepayBatchNo();
//        SunshineRepayBatchInput sunshineRepayBatchInput = new SunshineRepayBatchInput();
//        sunshineRepayBatchInput.setTotal(sunshineRepaymentBatch.getTotalCount());
//        sunshineRepayBatchInput.setAmount(sunshineRepaymentBatch.getTotalAmount().toPlainString());
//        sunshineRepayBatchInput.setRepaymentTime(LocalDate.now().toString());
//        sunshineRepayBatchInput.setOrgName(sunshineRepaymentBatch.getOrgName());
//        sunshineRepayBatchInput.setOrgCode(sunshineRepaymentBatch.getOrgCode());
//        sunshineRepayBatchInput.setRemark(sunshineRepaymentBatch.getRemark());
//        sunshineRepayBatchInput.setRepaymentId(repayBatchNo);
//        sunshineRepayBatchInput.setPathLink(downloadLink);
//        SunshineRepayBatchOutput sunshineRepayBatchOutput = null;
//        int status = 0;
//        // 修改文件为关联中
//        SunshineRepaymentBatch riUpdate = new SunshineRepaymentBatch();
//        riUpdate.setId(sunshineRepaymentBatch.getId());
//        riUpdate.setStatus(RepayBatchStatusEnum.FILE_ASSOCIATIONING.status);
//        sunshineRepaymentBatchMapper.updateById(riUpdate);
//
//        try {
//            sunshineRepayBatchOutput = crmApiManager.transFlowFile(sunshineRepayBatchInput);
//        } catch (Exception e) {
//            log.error("调用crm流水还款接口异常", e);
//            // 修改批次申请状态为异常
//            status = RepayBatchFileAssociationStatusEnum.FILE_ASSOCIATION_EXCEPTION.status;
//        }
//
//        if (sunshineRepayBatchOutput != null) {
//            if (sunshineRepayBatchOutput.isSuccess()) {
//                // 修改批次申请状态为成功
//                status = RepayBatchFileAssociationStatusEnum.FILE_ASSOCIATION_SUCCESS.status;
//            } else {
//                // 修改批次申请状态为失败
//                status = RepayBatchFileAssociationStatusEnum.FILE_ASSOCIATION_FAIL.status;
//            }
//        }
//        sunshineRepaymentBatchMapper.updateAssociationStatus(repayBatchNo, status);
//    }
//
//
//    /**
//     * 异步保存还款批次明细
//     *
//     * @param file
//     * @param repayBatchoNo
//     */
//    @Async("asyncServiceExecutor")
//    public void asyncSaveRepaymentDetail(MultipartFile file, String repayBatchoNo) {
//        LocalDateTime now = LocalDateTime.now();
//        // 获取数据
//        try {
//            //获取excel密码
//            SysDictData passwordDictData = sysDictDataService.selectDictDataByTypeAndLabel("sys_yg_file_password", "阳光excel还款文件密码");
//            EasyExcel.read(file.getInputStream(), SunshineReapyOrderDTO.class, new PageReadListener<SunshineReapyOrderDTO>(dataList -> {
//                List<SunshineRepaymentDetail> list = new ArrayList<>();
//                for (SunshineReapyOrderDTO sunshineOrder : dataList) {
//                    SunshineRepaymentDetail sunshineRepaymentDetail = new SunshineRepaymentDetail();
//                    sunshineRepaymentDetail.setRepayBatchNo(repayBatchoNo);
//                    sunshineRepaymentDetail.setSunshineOrderId(sunshineOrder.getOrdeId());
//                    sunshineRepaymentDetail.setCreateTime(now);
//                    list.add(sunshineRepaymentDetail);
//                }
//                sunshineRepaymentDetailService.saveBatch(list);
//            })).password(passwordDictData.getDictValue()).sheet().doRead();
//        } catch (Exception e) {
//            log.error("阳光还款明细保存异常",e);
//        }
//    }
//
//
//    /**
//     * 导入数据
//     *
//     * @param
//     */
//    @Transactional(rollbackFor = Exception.class)
//    //TODO 请确认
////    public SunshineRepaymentBatchFileDto dealSunshineRepayData(SunshineReapyOrderDTO bo) {
////        int dotIndex = bo.getFileSign().lastIndexOf('.');
////        String fileSign = "";
////        if (dotIndex > 0) {
////            fileSign = bo.getFileSign().substring(0, dotIndex);
////        }
////
////        if (org.apache.commons.lang3.StringUtils.isBlank(fileSign)) {
////            throw new BusinessException("【文件摘要格式不正确】");
////        }
////        String uuid = IdUtil.fastSimpleUUID();
////        try {
////            //加锁，同一时间只有一个任务在进行
////            Boolean absent = redisCache.setIfAbsent(fileSign, uuid, 5, TimeUnit.MINUTES);
////            if (!absent) {
////                throw new BusinessException("【存在正在进行的上传的任务】");
////            }
////
////            LambdaQueryWrapper<SunshineRepaymentBatch> lqw = Wrappers.lambdaQuery();
////            lqw.eq(SunshineRepaymentBatch::getFileSign, fileSign);
////            lqw.notIn(SunshineRepaymentBatch::getStatus, 0, 8, 9);
////            long integer = sunshineRepaymentBatchMapper.selectCount(lqw);
////            if (integer > 0) {
////                throw new BusinessException("【该文件已上传过】");
////            }
////
////            //校验
////            SunshineRepayMoneyAndTotalVO repaymentInventoryDTO = verifyExcelList(fileSign);
////
////            SunshineRepaymentBatch sunshineRepaymentBatch = new SunshineRepaymentBatch();
////            sunshineRepaymentBatch.setRepayBatchNo(IdUtil.getSnowflakeNextIdStr());
////            sunshineRepaymentBatch.setTotalAmount(repaymentInventoryDTO.getAmount());
////            sunshineRepaymentBatch.setTotalCount(repaymentInventoryDTO.getTotal());
////            sunshineRepaymentBatch.setStatus(2);
////            sunshineRepaymentBatch.setRemark(bo.getRemark());
////            //TODO 获取用户未实现
////            sunshineRepaymentBatch.setUploadBy(null);
////            sunshineRepaymentBatch.setFileSign(fileSign);
////
////            List<String> orgCodes = serviceOrderMapper.getOrgCode(fileSign);
////
////            if (orgCodes != null && orgCodes.size() == 1) {
////                sunshineRepaymentBatch.setOrgCode(orgCodes.get(0));
////            } else {
////                sunshineRepaymentBatch.setOrgCode(repaymentInventoryDTO.getOrgParentCode());
////            }
////            OrganizeYgry organizeYgry = organizeYgryMapper.selectOrganize(sunshineRepaymentBatch.getOrgCode());
////            if (organizeYgry == null) {
////                throw new BusinessException("【查询机构失败】");
////            }
////            BigDecimal taxTotalAmount = serviceOrderMapper.getTaxTotalAmount(fileSign);
////            sunshineRepaymentBatch.setTaxTotalAmount(taxTotalAmount);
////            sunshineRepaymentBatch.setOrgName(organizeYgry.getOrgName());
////            int insert = sunshineRepaymentBatchMapper.insert(sunshineRepaymentBatch);
////
////            if (insert <= 0) {
////                throw new BusinessException("【保存还款明细清单失败】");
////            }
////
////            SunshineRepaymentBatch riUpdate = new SunshineRepaymentBatch();
////            riUpdate.setId(sunshineRepaymentBatch.getId());
////            File file = new File(path + bo.getFileSign());
////            MultipartFile multipartFile;
////            try {
////                multipartFile = new MockMultipartFile(bo.getFileSign(), Files.newInputStream(file.toPath()));
////                String path = ossService.ossUpdate(multipartFile);
////
////                if (StringUtils.isBlank(path)) {
////                    throw new BusinessException("【文件保存路径为空】");
////                }
////                riUpdate.setOssPath(path);
////                riUpdate.setStatus(RepayBatchStatusEnum.FILE_ASSOCIATION_SUCCESS.status);
////                sunshineRepaymentBatchMapper.updateById(riUpdate);
////
////            } catch (Exception e) {
////                riUpdate.setStatus(RepayBatchStatusEnum.FILE_ASSOCIATION_FAIL.status);
////                sunshineRepaymentBatchMapper.updateById(riUpdate);
////                throw new BusinessException("【导入异常】:" + e.getMessage());
////            }
////            sunshineRepaymentBatch.setOssPath(riUpdate.getOssPath());
////            SunshineRepaymentBatchFileDto sunshineRepaymentBatchFileDto = new SunshineRepaymentBatchFileDto();
////            sunshineRepaymentBatchFileDto.setSunshineRepaymentBatch(sunshineRepaymentBatch);
////            sunshineRepaymentBatchFileDto.setMultipartFile(multipartFile);
////            return sunshineRepaymentBatchFileDto;
////        } catch (Exception e) {
////            throw new BusinessException("【导入异常】:" + e.getMessage());
////        } finally {
////            redisCache.unlock(fileSign, uuid);
////        }
////    }
//
//
//    public SunshineRepayMoneyAndTotalVO verifyExcelList(String fileSign) {
//        //查询订单
//        Integer totalByOrderIds = serviceOrderMapper.getTotalByOrderIds(fileSign);
//        if(totalByOrderIds > 0){
//            throw new BusinessException("【存在已还款订单】");
//        }
//
//        List<SunshineRepayMoneyAndTotalVO> result = serviceOrderMapper.getAmountAndTotalByOrderIds(fileSign);
//        if (result == null || result.isEmpty()) {
//            throw new BusinessException("【上传文件订单数量不匹配】");
//        }
//
//        if (result.size() != 1) {
//            throw new BusinessException("【上传文件订单二级机构不唯一】");
//        }
//
//        SunshineRepayMoneyAndTotalVO repaymentInventoryDTO = result.get(0);
//        if (repaymentInventoryDTO.getTotal() != sunshineRepaymentFileDataService.getCountByFileSign(fileSign)) {
//            throw new BusinessException("【上传文件订单数量不匹配, 可能存在已还款订单】");
//        }
//
//        return repaymentInventoryDTO;
//    }
//
//}
