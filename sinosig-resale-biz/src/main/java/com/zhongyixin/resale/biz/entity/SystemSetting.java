package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SystemSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置名
     */
    private String itemName;

    /**
     * 配置值
     */
    private String itemValue;

    /**
     * 启用状态（1.启用；0.禁用）
     */
    private Boolean enabled;

    private String remark;

    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识：1.已删除；0.未删除；
     */
    private Boolean deleted;


}
