package com.zhongyixin.resale.biz.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zhongyixin.resale.biz.common.annotation.ExcelConverterTyep;
import com.zhongyixin.resale.biz.common.excel.converter.ExcelDictConverter;
import com.zhongyixin.resale.biz.common.excel.converter.ExcelDictStringConverter;
import com.zhongyixin.resale.biz.common.excel.converter.LocalDateConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 阳光结算任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SinosigDaySettleTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ExcelIgnore
    private Integer id;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelIgnore
    private LocalDateTime updateTime;

    /**
     * 推送状态
     */
    @ExcelProperty(value = "推送状态",converter = ExcelDictConverter.class)
    @ExcelConverterTyep(type = "推送状态")
    private Integer status;

    /**
     * 记录号
     */
    @ExcelProperty("记录号")
    private String recordNo;

    /**
     * 文件地址
     */
    @ExcelIgnore
    private String fileUrl;

    /**
     * 文件名称
     */
    @ExcelProperty("推送文件")
    private String fileName;

    /**
     * 推送时间
     */
    @ExcelProperty("推送时间")
    private LocalDateTime pushTime;

    /**
     * 结算时间
     */
    @ExcelProperty(value = "结算日期",converter = LocalDateConverter.class)
    private LocalDate settleDate;

    /**
     * 总金额
     */
    @ExcelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 含服总金额
     */
    @ExcelProperty("含服总金额")
    private BigDecimal totalTaxAmount;

    /**
     * 供应商编号
     */
    @ExcelProperty("供应商编号")
    private String supplierNo;

    /**
     * 供应商编码
     */
    @ExcelProperty("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelIgnore
    private String supplierName;

    /**
     * 结算方式
     */
    @ExcelProperty(value = "结算方式",converter = ExcelDictStringConverter.class)
    @ExcelConverterTyep(type = "结算方式")
    private String settleType;

    /**
     * 文件摘要
     */
    @ExcelIgnore
    private String fileSign;

    /**
     * 删除记号
     */
    @ExcelIgnore
    private String deleted;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;


}
