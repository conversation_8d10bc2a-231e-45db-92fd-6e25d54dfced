package com.zhongyixin.resale.biz.config;

import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.Lock.RedissonLockManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApplicationConfiguration {

    @Bean
    LockManager lockManager(DLockFactory<?> dLockFactory) {
        return new RedissonLockManager(dLockFactory);
    }


}
