package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.OrderWriteOff;

import java.util.List;

/**
 * <p>
 * 订单核销表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface OrderWriteOffService extends IService<OrderWriteOff> {

    OrderWriteOff selectOneByOrderId(String orderId);

    void updateOldOrderWriteOff();

    void retry(String orderId);

    boolean updateVasCouponStatus(OrderWriteOff orderWriteOff, List<Integer> originalStatusList);

}
