package com.zhongyixin.resale.biz.common.constant;

/**
 * <AUTHOR>
 * @create 2023/2/10 16:14
 */
public interface PaymentAisleConstant {

    /**
     * 支付方式：银行卡
     */
    String BANKCARD_PAY_WAY = "银行卡";

    /**
     * 支付方式：支付宝
     */
    String ALIPAY_PARY_WAY = "支付宝";

    /**
     * 币种：人民币
     */
    String CNY_CURRENCY_CODE = "RMB";

    interface CMBC {

        /**
         * 支付通道：民生银行
         */
        String PAY_COMPANY = "民生银行";

        /**
         * 收款账户类型：借记卡
         */
        String DEBITCARD_PAYEEACC_TYPE = "1";

        /**
         * 收款账户开户行：民生银行总行号
         */
        String PAYEE_PARTYID = "************";


        /**
         * 流水号长度
         */
        String MERCHANTSEQ_SIZE = "50";

        /**
         * 填充字符串
         */
        String PADSTR = "0";

        /**
         * 退汇通知类型
         */
        String REEXCHANGE_SVRTYPE = "7";

        /**
         * 代付通知类型
         */
        String PAYMENT_SVRTYPE = "8";

        /**
         * 通知:交易成功
         */
        String NOTIFY_SUCCESS_STATUS = "A";

        /**
         * 通知:交易成功
         */
        String NOTIFY_SUCCESS_STATUS_1 = "S";

        /**
         * 通知:交易失败
         */
        String NOTIFY_FAIL_STATUS = "B";

        /**
         * 通知:交易失败
         */
        String NOTIFY_FAIL_STATUS_1 = "E";

        /**
         * 查询：交易成功
         */
        String PAY001_SUCCESS_STATUS = "40";

        /**
         * 查询：交易失败
         */
        String PAY001_FAIL_STATUS = "42";

        /**
         * 查询：交易失败
         */
        String PAY001_FAIL_STATUS_02 = "02";


    }

    interface Lakala {
        /**
         * 支付通道：拉卡拉
         */
        String PAY_COMPANY = "拉卡拉";

        /**
         * 查询：交易成功
         */
        String SUCCESS_STATUS = "S";

        /**
         * 查询：交易成功
         */
        String FAIL_STATUS = "F";
    }

    interface NewLakala {
        /**
         * 支付通道：拉卡拉
         */
        String PAY_COMPANY = "拉卡拉-new-boss";

    }

    interface LianlianPay {
        /**
         * 支付通道：连连支付
         */
        String PAY_COMPANY = "连连支付-珀延";

        /**
         * 支付通道：连连支付
         */
        String PAY_COMPANY_QUNQIU = "连连支付-群秋";

        /**
         * 查询：交易成功
         */
        String SUCCESS_STATUS = "S";

        /**
         * 查询：交易成功
         */
        String FAIL_STATUS = "F";
    }

    interface Yinshang {
        /**
         * 支付通道：银商
         */
        String PAY_COMPANY = "银商";

        /**
         * 查询：交易成功
         */
        String SUCCESS_STATUS = "S";

        /**
         * 查询：交易成功
         */
        String FAIL_STATUS = "F";
    }

}
