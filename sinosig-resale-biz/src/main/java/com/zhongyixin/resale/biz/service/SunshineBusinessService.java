package com.zhongyixin.resale.biz.service;


import com.wftk.common.core.result.ApiResult;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.OrganizeYgryVO;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoQueryVO;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoVO;
import com.zhongyixin.resale.biz.entity.Issue;

/**
 * <p>
 * 阳光服务类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface SunshineBusinessService {

    /**
     * 查询是否有发布计划（阳光）
     *
     * @param apiSource
     * @param orderType
     * @return
     */
    ApiResult<Issue> queryIssue(String apiSource, String orderType);

    ApiResult<OrganizeYgryVO> queryOrgQuato(OrgnzieQuatoQueryDTO orgnzieQuatoQueryDTO);


    ApiResult<SunshineUserPointInfoQueryVO> querySinosigOrderPointLogicDeal(SunshineUserInfoQueryDTO sunshineUserInfoQueryDTO);

    /**
     * 领取接口
     *
     * @param sunshineReceiveCardRollsDTO
     * @return
     */
    String receiveCardRolls(SunshineReceiveCardRollsDTO sunshineReceiveCardRollsDTO);
}
