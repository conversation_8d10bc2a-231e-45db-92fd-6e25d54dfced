package com.zhongyixin.resale.biz.ext.sinosig.signature;

import cn.hutool.crypto.SecureUtil;
import com.wftk.signature.builder.BaseSignBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2024/3/6 17:26
 */
@Slf4j
public class SinosigUpdateSortSignBuilder extends BaseSignBuilder {

    private final String secret;

    public SinosigUpdateSortSignBuilder(String secret) {
        super(secret);
        this.secret = secret;
    }

    @Override
    public String build() {
        Map<String, Object> newMap = entrySet().stream().filter((e) -> e.getValue() != null && e.getValue() != "").collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue));
        clear();
        addParams(newMap);
        remove("sign");
        String params = StringUtils.join(entrySet(), "&");
        params = StringUtils.join(params, "&key=" + secret);
        log.info("sign before: [{}]", params);
        String sign = SecureUtil.md5(params).toUpperCase();
        log.info("after sign: [{}]", sign);
        return sign;
    }


}
