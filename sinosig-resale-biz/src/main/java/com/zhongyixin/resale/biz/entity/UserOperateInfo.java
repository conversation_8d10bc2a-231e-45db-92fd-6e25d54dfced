package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_user_operate_info")
public class UserOperateInfo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * 设备
     */
    private String device;

    /**
     * 支付订单id
     */
    private String orderId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 所在地
     */
    private String location;

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 客户类型
     */
    private String orderType;


}
