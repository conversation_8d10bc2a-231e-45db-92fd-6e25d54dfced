package com.zhongyixin.resale.biz.common.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class OrganizeInfoVO {

    /**
     * 机构id
     */
    private Integer id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 总额度
     */
    private BigDecimal totalAmount;

    /**
     * 总消耗额度
     */
    private BigDecimal consumeAmount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 余额百分比
     */
    private String balanceRate;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 限额周期
     */
    @Schema(description = "限额周期（单位：天）")
    private Integer quotaPeriod;

    /**
     * 维护状态(0.正常 1.维护中)
     */
    private Integer preserveStatus;

    /**
     * 最大帐期天数
     */
    private Long maxAccountDays;

}
