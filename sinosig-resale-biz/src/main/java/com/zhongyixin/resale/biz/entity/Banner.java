package com.zhongyixin.resale.biz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Banner implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 图片路径
     */
    private String image;

    /**
     * 状态：0.下线 1.正常
     */
    private Integer status;

    /**
     * 类型：1.轮播图 2.首页分类
     */
    private Integer type;

    /**
     * 跳转信息
     */
    private String jumpMsg;

    /**
     * 来源
     */
    private String apiSource;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    private Integer deleted;


}
