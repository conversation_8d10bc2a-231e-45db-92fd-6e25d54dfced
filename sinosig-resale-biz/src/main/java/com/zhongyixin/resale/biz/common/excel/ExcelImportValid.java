package com.zhongyixin.resale.biz.common.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author:lzp
 * @create: 2022-08-02 10:30
 * @Description: excel字段校验类
 */

public class ExcelImportValid {

    /**
     * Excel导入字段校验
     * @param object 校验的JavaBean 其属性须有自定义注解
     */
    public static void valid(Object object) throws Exception {
        //获取当前类的所有属性
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //设置可访问
            field.setAccessible(true);
            //属性的值
            Object fieldValue;
            try {
                //获取到字段的值
                fieldValue = field.get(object);
            } catch (IllegalAccessException e) {
                throw new BusinessException("导入参数检查失败");
            }
            //是否包含必填校验注解
            boolean isExcelValid = field.isAnnotationPresent(ExcelValid.class);
            //如果包含这个注解，并且获取到的值为null，抛出异常
            if (isExcelValid && Objects.isNull(fieldValue)) {
                throw new BusinessException("NULL"+ field.getAnnotation(ExcelValid.class).message());
            }
        }
    }

    /**
     * Excel导入字段校验
     * @param object 校验的JavaBean 其属性须有自定义注解
     */
    public static void replenishValid(Object object,AnalysisContext context) throws Exception {
        List<String> errInfo = new ArrayList<>();
        //获取当前类的所有属性
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //设置可访问
            field.setAccessible(true);
            //是否包含必填校验注解
            boolean isExcelValid = field.isAnnotationPresent(ExcelValid.class);
            if(isExcelValid){
                //属性的值
                Object fieldValue;
                try {
                    //获取到字段的值
                    fieldValue = field.get(object);
                } catch (IllegalAccessException e) {
                    throw new BusinessException("导入参数检查失败");
                }
                //如果包含这个注解，并且获取到的值为null，抛出异常
                if (Objects.isNull(fieldValue) || fieldValue.toString().isEmpty()) {
                    errInfo.add("第"+(context.readRowHolder().getRowIndex()+1)+"行"+field.getAnnotation(ExcelValid.class).message());
                }
            }
        }
        if(errInfo.size()>0){
            throw new BusinessException(JSONObject.getInstance().toJSONString(errInfo));
        }
    }
}

