package com.zhongyixin.resale.biz.manage;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.common.constant.OrderConstant;
import com.zhongyixin.resale.biz.common.constant.PaymentAisleConstant;
import com.zhongyixin.resale.biz.common.constant.VasOrderPublishRecordConstant;
import com.zhongyixin.resale.biz.common.converter.CommonDtoToVoConvert;
import com.zhongyixin.resale.biz.common.enums.ErrorCodeEnum;
import com.zhongyixin.resale.biz.common.exception.SinosigApiBadBizException;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.entity.VasOrderPublishRecord;
import com.zhongyixin.resale.biz.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * copy form SunshineBusinessServiceImpl
 * <AUTHOR>
 * @create 2023/3/30 15:25
 */
@Slf4j
@Component
public class SunshineOrderManager {

    @Autowired
    OrderService orderService;

    @Autowired
    ServiceOrderService serviceOrderService;

    @Autowired
    PayOrderLianlianService payOrderLianlianService;

    @Autowired
    PayRecordService payRecordService;

    @Autowired
    VasOrderPublishRecordService vasOrderPublishRecordService;


    @Transactional(noRollbackFor = BusinessException.class)
    public PayOrderLianlian createLianLianOrder(Order order) {
        PayOrderLianlian payOrderLianlian = payOrderLianlianService.findUnfinishedOrder(order.getOrderId());
        if (payOrderLianlian == null) {
            String merchantSeq = IdUtil.getSnowflakeNextIdStr();
            //生成连连支付订单
            payOrderLianlian = CommonDtoToVoConvert.INSTANCE.orderToPayOrderLianlian(order);
            payOrderLianlian.setPayOrderId(merchantSeq);
            payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.INIT_ORDER_STATUS));
            payOrderLianlianService.save(payOrderLianlian);
            //新增支付记录
            payRecordService.insertPayRecord(order,payOrderLianlian.getStatus(),
                    payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY,null);

            //新增投放记录
            VasOrderPublishRecord vasOrderPublishRecord = new VasOrderPublishRecord();
            vasOrderPublishRecord.setOrderId(order.getOrderId());
            vasOrderPublishRecord.setChildOrderNo(String.valueOf(payOrderLianlian.getId()));
            vasOrderPublishRecord.setTradeNo(payOrderLianlian.getPayOrderId());
            vasOrderPublishRecord.setOrderType(VasOrderPublishRecordConstant.OrderType.BANK);
            vasOrderPublishRecord.setTel(order.getTelphone());
            vasOrderPublishRecord.setBankNo(order.getBankCardNo());
            vasOrderPublishRecord.setUserName(order.getUserName());
            vasOrderPublishRecord.setAmount(order.getAmount());
            vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.AWAIT);
            vasOrderPublishRecordService.save(vasOrderPublishRecord);

        } else {
            if (payOrderLianlian.getStatus() != Integer.parseInt(OrderConstant.INIT_ORDER_STATUS)) {
                throw new BusinessException("订单处理中");
            }
        }
        return payOrderLianlian;
    }

    @Transactional
    public void changeSinosigStatusToStart(Order order, ServiceOrder serviceOrder, String payCompany, String redirectUrl, String bankCardNo) {
        // 修改order订单为处理中（领取中）
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setRedirectUrl(redirectUrl);
        updateOrder.setBankCardNo(bankCardNo);
        updateOrder.setStatus(Integer.valueOf(OrderConstant.DEALING_ORDER_STATUS));
        updateOrder.setPayCompany(payCompany);
        Integer i = orderService.updateOrderById(updateOrder);
        //修改失败
        if (i < 1) {
            throw new BusinessException("订单已被处理！");
        }

        //修改阳光订单状态
        serviceOrder.setStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_PROCESSED));
        serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_PROCESSED));
        serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);

    }

//    @Transactional
//    public void changeSinosigStatusToException(Order order,ServiceOrder serviceOrder,String payOrderId,String tranResult,String payCompany,boolean flag) {
//        //1.修改订单状态为异常
//        Order modifedOrder = new Order();
//        modifedOrder.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
//        modifedOrder.setId(order.getId());
//        orderService.updateOrderByIdAndDealing(modifedOrder);
//
//        //修改阳光订单状态
//        serviceOrder.setStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED));
//        serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED));
//        serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
//
//        //修改机构维护状态
//        if(flag){
//            OrganizeYgry organizeYgry = new OrganizeYgry();
//            organizeYgry.setPreserveStatus(CommonConstant.OrganizeYgry.PRESERVE_STATUS_PRESERVING);
//            organizeYgry.setOrgCode(serviceOrder.getOrgParentCode());
//            organizeYgryService.updatePreserveStatus(organizeYgry);
//        }
//
//        //新增支付记录
//        payRecordService.insertPayRecord(order,Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS),
//                payOrderId,payCompany,tranResult);
//    }





    @Transactional
    public void updateOrder(Order order, UpdateOrderDTO dto){
        Order unfinishedOrder = orderService.selectOrderByCardOrderNo(dto.getOrderId());
        if (unfinishedOrder.getStatus() != Integer.parseInt(OrderConstant.INIT_ORDER_STATUS)
                && unfinishedOrder.getStatus() != Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS)) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_UPDATE.getCode(), ErrorCodeEnum.NOT_UPDATE.getMessage());
        }

        if (!orderService.updateOrder(dto)) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_UPDATE.getCode(), ErrorCodeEnum.NOT_UPDATE.getMessage());
        }
        if (!serviceOrderService.updateOrder(dto)) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_UPDATE.getCode(), ErrorCodeEnum.NOT_UPDATE.getMessage());
        }

        //TODO 是否需要更换
        //更换投放方式
//        String sinosigPayWay = commonService.getSinosigPayWay(dto.getOrderId(), order.getTelphone(), dto.getModifyToName(), dto.getModifyToIdNo());
//        Order orderInDb = new Order();
//        orderInDb.setPayWay(sinosigPayWay);
//        orderInDb.setId(order.getId());
//        orderService.updateOrderById(orderInDb);
    }



}
