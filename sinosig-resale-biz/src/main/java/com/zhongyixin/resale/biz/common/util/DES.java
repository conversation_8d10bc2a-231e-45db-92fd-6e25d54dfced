/**
 * DES加密解密.
 * 
 * @title: DES.java
 * <AUTHOR>
 * @date 2013-5-2 9:27:55
 */
package com.zhongyixin.resale.biz.common.util;

import org.apache.log4j.Logger;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.SecureRandom;

/**
 * DES加密解密.
 * 
 * @className DES
 * <AUTHOR>
 * @version V1.0 2013-5-2 9:27:55 TODO(如果是修改版本，描述修改内容)
 */
public final class DES
{
    // private static final String PASSWORD_CRYPT_KEY = "%^&*RG*D";
    private static final String DES = "DES";
    private static final String ENCODING = "UTF-8";
    private static final Logger LOGGER = Logger.getLogger(DES.class);

    private DES()
    {
    }

    /**
     * 加密.
     * 
     * @param src 数据源
     * @param key 密钥，长度必须是8的倍数
     * @return 返回加密后的数据
     * @throws Exception the exception
     */
    public static byte[] encrypt(byte[] src, byte[] key) throws Exception
    {
        // DES算法要求有一个可信任的随机数源
        SecureRandom sr = new SecureRandom();
        // 从原始密匙数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);
        // 创建一个密匙工厂，然后用它把DESKeySpec转换成
        // 一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(DES);
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, sr);
        // 现在，获取数据并加密
        // 正式执行加密操作
        return cipher.doFinal(src);

    }

    /**
     * 解密.
     * 
     * @param src 数据源
     * @param key 密钥，长度必须是8的倍数
     * @return 返回解密后的原始数据
     * @throws Exception the exception
     */
    public static byte[] decrypt(byte[] src, byte[] key) throws Exception
    {
        // DES算法要求有一个可信任的随机数源
        SecureRandom sr = new SecureRandom();
        // 从原始密匙数据创建一个DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);
        // 创建一个密匙工厂，然后用它把DESKeySpec对象转换成
        // 一个SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);
        // Cipher对象实际完成解密操作
        Cipher cipher = Cipher.getInstance(DES);
        // 用密匙初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, securekey, sr);
        // 现在，获取数据并解密
        // 正式执行解密操作
        return cipher.doFinal(src);

    }

    /**
     * 密码解密.
     * 
     * @param data the data
     * @param key the key
     * @return the string
     */
    public static String decrypt(String data, String key)
    {
        try
        {
            return new String(decrypt(hex2byte(data.getBytes(ENCODING)), key.getBytes(ENCODING)), ENCODING);
        }
        catch (Exception e)
        {
            LOGGER.error("使用DES解密时出现异常",e);
        }
        return null;

    }

    /**
     * 密码加密.
     * 
     * @param password the password
     * @param key the key
     * @return the string
     */
    public static String encrypt(String password, String key)
    {
        try
        {
            return byte2hex(encrypt(password.getBytes(ENCODING), key.getBytes(ENCODING)));
        }
        catch (Exception e)
        {
            LOGGER.error("使用DES加密时出现异常",e);
        }
        return null;

    }

    /**
     * 二行制转字符串.
     * 
     * @param b the b
     * @return the string
     */
    private static String byte2hex(byte[] b)
    {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++)
        {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
            {
                hs = hs + "0" + stmp;
            }
            else
            {
                hs = hs + stmp;
            }
        }
        return hs.toUpperCase();
    }

    /**
     * Hex2byte.
     * 
     * @param b the b
     * @return the byte[]
     */
    private static byte[] hex2byte(byte[] b)
    {
        if ((b.length % 2) != 0)
        {
            throw new IllegalArgumentException("长度不是偶数");
        }
        byte[] b2 = new byte[b.length / 2];
        for (int n = 0; n < b.length; n += 2)
        {
            String item = new String(b, n, 2);
            b2[n / 2] = (byte) Integer.parseInt(item, 16);
        }
        return b2;
    }

    /**
     * The main method.
     * 
     * @param args the arguments
     * @throws UnsupportedEncodingException the unsupported encoding exception
     */
    // public static void main(String[] args) throws UnsupportedEncodingException
    // {
    // String enStr = encrypt("cust_source=01&cust_type=01", "fdsafezfdsaf");
    // System.out.println("md5加密后的字符串是       " + enStr);
    // String deStr = decrypt(enStr, "fdsafezfdsaf");
    // System.out.println("md5解密后的字符串是       " + deStr);
    // }
}
