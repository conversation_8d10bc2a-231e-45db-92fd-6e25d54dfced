package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 银行卡信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BankCardInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 银行卡号
     */
    private String cardNo;

    /**
     * 银行卡归属编码
     */
    private String bankCode;

    /**
     * 银行卡归属名称
     */
    private String bankName;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡类型
     */
    private String cardType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    private Integer deleted;

    /**
     * 备注
     */
    private String remark;


}
