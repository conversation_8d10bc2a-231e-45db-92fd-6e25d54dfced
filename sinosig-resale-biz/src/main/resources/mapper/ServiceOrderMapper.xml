<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.ServiceOrderMapper">

    <sql id="selectServiceOrder">
        select
            id,order_id,coupon_no,amount,user_name,telephone,bank_card_no,id_card,create_time,tran_date,
            org_code,org_parent_code,payment_no,status,repay_status,repay_id,repay_date,start_rate_date,end_rate_date,
            ppid,customer_no,service_eff_date,service_exp_date,provider_name,api_source,source,pay_company,in_company_ip,
            in_company_credit_code,sunshine_service_charge,channel,agent_code,agent_name,order_mode
        from
            tb_service_order
    </sql>

    <sql id="selectServiceOrderVO">
        SELECT
            id,
            create_time,
            order_id,
            amount,
            user_name,
            telephone,
            bank_card_no,
            id_card,
            tran_date,
            payment_no,
            org_parent_code,
            pay_company,
            org_code,
            provider_name,
            repay_date,
            repay_id,
            end_rate_date,
            repay_status,
            ifnull(
                    case order_mode
                        when 1 then
                            case pay_company
                                when "支付宝" then
                                    if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d'))-29)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d'))-29),0)
                                else
                                    (DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1)
                                end
                        when 2 then
                            if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1),0)
                        end,
                    0
                ) AS advance_fund_Day,
            round(
                        amount * ifnull(
                            case order_mode
                                when 1 then
                                    case pay_company
                                        when "支付宝" then
                                            if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d'))-29)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d'))-29),0)
                                        else
                                            (DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1)
                                        end
                                when 2 then
                                    if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,DATE_FORMAT(start_rate_date,'%Y-%m-%d')) + 1),0)
                                end,
                            0
                        )* 0.0003,
                        2
                ) AS interest,
             order_mode
        FROM
            tb_service_order
    </sql>

    <sql id="serviceOrderCondition">
        <where>
            <if test="serviceOrderSearchDTO.createStartDate!=null and serviceOrderSearchDTO.createEndDate!= null ">
                and create_time between DATE_FORMAT( #{serviceOrderSearchDTO.createStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.createEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.repayStartDate!=null and serviceOrderSearchDTO.repayEndDate!= null ">
                and end_rate_date between DATE_FORMAT( #{serviceOrderSearchDTO.repayStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.repayEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.tranStartDate!=null and serviceOrderSearchDTO.tranEndDate!= null ">
                and tran_date between DATE_FORMAT( #{serviceOrderSearchDTO.tranStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.tranEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.repayId!=null and serviceOrderSearchDTO.repayId!= '' ">
                and repay_id = #{serviceOrderSearchDTO.repayId}
            </if>
            <if test="serviceOrderSearchDTO.orderId!=null and serviceOrderSearchDTO.orderId!= '' ">
                and order_id = #{serviceOrderSearchDTO.orderId}
            </if>
            <if test="serviceOrderSearchDTO.orgCode!=null and serviceOrderSearchDTO.orgCode!= '' ">
                and org_code = #{serviceOrderSearchDTO.orgCode}
            </if>
            <if test="serviceOrderSearchDTO.orgParentCode!=null and serviceOrderSearchDTO.orgParentCode!= '' ">
                and org_parent_code = #{serviceOrderSearchDTO.orgParentCode}
            </if>
            <if test="serviceOrderSearchDTO.telephone!=null and serviceOrderSearchDTO.telephone!= '' ">
                and telephone = #{serviceOrderSearchDTO.telephone}
            </if>
            <if test="serviceOrderSearchDTO.repayStatus!=null or serviceOrderSearchDTO.repayStatus== 0 ">
                and repay_status = #{serviceOrderSearchDTO.repayStatus}
            </if>
            <if test="serviceOrderSearchDTO.providerName!=null and serviceOrderSearchDTO.providerName!= '' ">
                and provider_name = #{serviceOrderSearchDTO.providerName}
            </if>
            <if test="serviceOrderSearchDTO.paymentNo!=null and serviceOrderSearchDTO.paymentNo!= '' ">
                and payment_no = #{serviceOrderSearchDTO.paymentNo}
            </if>
            <if test="serviceOrderSearchDTO.payCompany != null and serviceOrderSearchDTO.payCompany != ''">
                and pay_company = #{serviceOrderSearchDTO.payCompany}
            </if>
            <if test="serviceOrderSearchDTO.orderMode != null">
                and order_mode = #{serviceOrderSearchDTO.orderMode}
            </if>
        </where>
    </sql>

    <select id="selectServiceOrderByOrderIdAndPhone"
            resultType="com.zhongyixin.resale.biz.common.input.SunshineOrderStatusDTO">
        select
            tso.id,tbo.card_order_no orderId,tbo.user_name userName,tso.telephone telephone,
            tbo.bank_card_no bankCardNo,tso.amount amount,tso.create_time createTime,
            tso.`status` status,tso.tran_date tranDate,tso.coupon_no couponNo,tso.pay_company payCompany
        from tb_order tbo,tb_service_order tso
        where
             tbo.card_order_no = tso.order_id and tbo.api_source = 'RONGZHI'
          <if test="orderId != null and orderId != ''">
             and tso.order_id = #{orderId}
          </if>
          <if test="phone != null and phone != ''">
              and tso.telephone = #{phone}
          </if>
          <if test="payCompany != null and payCompany != ''">
              and tso.pay_company = #{payCompany}
          </if>
    </select>

    <update id="updateServiceOrderRepayStatus">
        update tb_service_order
            set status = #{status} ,repay_status = #{repayStatus}
        <if test="sunshineServiceCharge != null and sunshineServiceCharge != ''">
            , sunshine_service_charge = #{sunshineServiceCharge}
        </if>
        <if test="paymentNo != null and paymentNo != ''">
            , payment_no = #{paymentNo}
        </if>
        <if test="payCompany != null and payCompany != ''">
            , pay_company = #{payCompany}
        </if>
        <if test="tranDate != null">
            , tran_date = #{tranDate}
        </if>
        <if test="startRateDate != null">
            , start_rate_date = #{startRateDate}
        </if>
        where order_id = #{orderId}
    </update>

    <select id="selectServiceOrderByOrderId" resultType="com.zhongyixin.resale.biz.entity.ServiceOrder">
        <include refid="selectServiceOrder"/>
        where order_id = #{orderId}
    </select>
    <select id="selectServiceOrderInfoVO" resultType="com.zhongyixin.resale.biz.common.output.ServiceOrderInfoVO">
        <include refid="selectServiceOrderVO"/>
        <include refid="serviceOrderCondition"/>
        order by tran_date desc, create_time desc
    </select>
    <select id="selectServiceOrderInfoVOList"
            resultType="com.zhongyixin.resale.biz.common.output.ServiceOrderInfoVO">
        <include refid="selectServiceOrderVO"/>
        <include refid="serviceOrderCondition"/>
        order by tran_date desc, create_time desc
        limit #{offset}, #{batchSize}
    </select>
    <select id="statisticsServiceOrderTotalData"
            resultType="com.zhongyixin.resale.biz.common.output.ServiceOrderStatisticsDataVO">
        select count(*) total_count,sum(amount) total_amount, sum(round(ifnull(amount,0) * (1 +
        convert(ifnull(sunshine_service_charge,0),decimal(16,6))/100),2)) total_tax,
        sum( round(
        amount * ifnull(
        case order_mode
        when 1 then
        case pay_company
        when "支付宝" then
        if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date)-29)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date)-29),0)
        else
        if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date) + 1)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date) + 1),0)
        end
        when 2 then
        if((DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date) + 1)>0,(DATEDIFF(CASE WHEN end_rate_date IS NULL THEN now() ELSE end_rate_date END,start_rate_date) + 1),0)
        end,
        0
        )* 0.0003,
        2
        )) total_interest
        from tb_service_order
        <include refid="serviceOrderCondition"/>
    </select>

    <select id="statisticsServiceOrderTotalCount" resultType="java.lang.Integer">
        select count(*)
        from tb_service_order
        <include refid="serviceOrderCondition"/>
    </select>

    <select id="getAmountByOrg" resultType="java.math.BigDecimal">
        select sum(amount) from tb_service_order where repay_status = #{repayStatus} and org_parent_code = #{parentOrgCode} and amount is not null
    </select>
    <select id="getAmountByOrgCode" resultType="java.math.BigDecimal">
        select amount from tb_service_order where repay_status in (1,2) and org_parent_code = #{parentOrgCode} and amount is not null
    </select>
    <select id="getOrderNumbserByOrg" resultType="java.lang.Integer">
        select count(*) from tb_service_order where repay_status = #{repayStatus} and org_parent_code = #{parentOrgCode} and tran_date is not null and start_rate_date <![CDATA[ < ]]> #{tranDate}
    </select>
    <select id="getAmountByOrgParentCode" resultType="java.math.BigDecimal">
        select sum(amount) from tb_service_order where repay_status in (1,2) and org_parent_code = #{parentOrgCode} and amount is not null
    </select>
    <select id="listCopyOrder" resultType="com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO">
        <include refid="selectServiceOrder"/>
        where order_id in
        <foreach item = "item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateOrderInfoByOrderId">
       update  tb_service_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
        </trim>
        WHERE order_id = #{orderId}
    </update>

    <update id="updateBatchInsIpByOrderIds">
        update tb_service_order
        set in_company_ip = CASE order_id
        <foreach collection="list" item="item" index="index">
            when #{item.orderId} THEN #{item.inCompanyIp}
        </foreach>
        end,
        customer_no = CASE order_id
        <foreach collection="list" item="item" index="index">
            when #{item.orderId} THEN #{item.customerNo}
        </foreach>
        end
        where order_id in (
        <foreach collection="list" separator="," item="item" index="index">
            #{item.orderId}
        </foreach>
        )
    </update>
    <update id="updateOrderRepayInfo">
        update tb_service_order tso, tb_sunshine_repayment_detail tsrd
            set tso.repay_status = #{serviceOrder.repayStatus},
                tso.repay_id = #{serviceOrder.repayId},
                tso.end_rate_date = #{serviceOrder.endRateDate},
                tso.repay_date = #{serviceOrder.repayDate}
        where tso.order_id = tsrd.sunshine_order_id and tsrd.repay_batch_no = #{repayBatchNo}
    </update>


    <select id="getAmountAndTotalByOrderIds" resultType="com.zhongyixin.resale.biz.common.output.SunshineRepayMoneyAndTotalVO">
        SELECT
            sum( amount ) as amount, count(*) as total , org_parent_code as orgParentCode
        FROM
            tb_service_order
        WHERE
        order_id IN ( SELECT DISTINCT sunshine_order_id FROM tb_sunshine_repayment_file_data WHERE file_sign = #{fileSign} )
        and repay_status = 2 GROUP BY org_parent_code
    </select>


    <select id="getOrgCode" resultType="string">
        SELECT
        org_code as orgCode
        FROM
        tb_service_order
        WHERE
        order_id IN ( SELECT DISTINCT sunshine_order_id FROM tb_sunshine_repayment_file_data WHERE file_sign = #{fileSign} )
        GROUP BY org_code
    </select>
    <select id="calcNonRepayOrderAmount" resultType="java.math.BigDecimal">
        select
            sum(tso.amount)
        from tb_service_order tso
        where tso.order_id in
              (select tsrd.sunshine_order_id from tb_sunshine_repayment_detail tsrd
              where tsrd.repay_batch_no = #{repayBatchNo} )
          and tso.repay_status = 2
    </select>
    <select id="selectServiceOrderCopyDTOByOrderId"
            resultType="com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO">
        <include refid="selectServiceOrder"/>
        where order_id = #{orderId}
    </select>



    <update id="updateOrder">
        update tb_service_order set user_name = #{modifyToName},id_card = #{modifyToIdNo} where order_id = #{orderId} and status in (0,9)
    </update>
    <update id="cancelOrder">
        update tb_service_order set status = 7 where order_id = #{orderId} and status = 0
    </update>
    <update id="deferredServiceOrder">
        update tb_service_order set service_exp_date = #{serviceExpDate}
                                where order_id = #{orderId} and status in (0,9)
    </update>


    <select id="dailyOrderStatistics" resultType="com.zhongyixin.resale.biz.common.output.AmountAndCountVO">
        select ifnull(sum(amount),0) as amount,count(*) as count from tb_service_order
        where tran_date >= #{startDate} and tran_date &lt;= concat(#{endDate},' 23:59:59')
          and (agent_code is null or agent_code = '')
        <if test="facilitator != null and facilitator != '' ">
            and provider_name = #{facilitator}
        </if>
    </select>


    <sql id="serviceOrderConditionV2">
        <where>
            and create_time between  '2023-01-01 00:00:00'  and  '2024-03-31 23:59:59'
            and amount &lt;= 200
            <if test="serviceOrderSearchDTO.createStartDate!=null and serviceOrderSearchDTO.createEndDate!= null ">
                and create_time between DATE_FORMAT( #{serviceOrderSearchDTO.createStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.createEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.repayStartDate!=null and serviceOrderSearchDTO.repayEndDate!= null ">
                and end_rate_date between DATE_FORMAT( #{serviceOrderSearchDTO.repayStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.repayEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.tranStartDate!=null and serviceOrderSearchDTO.tranEndDate!= null ">
                and tran_date between DATE_FORMAT( #{serviceOrderSearchDTO.tranStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{serviceOrderSearchDTO.tranEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="serviceOrderSearchDTO.repayId!=null and serviceOrderSearchDTO.repayId!= '' ">
                and repay_id = #{serviceOrderSearchDTO.repayId}
            </if>
            <if test="serviceOrderSearchDTO.orderId!=null and serviceOrderSearchDTO.orderId!= '' ">
                and order_id = #{serviceOrderSearchDTO.orderId}
            </if>
            <if test="serviceOrderSearchDTO.orgCode!=null and serviceOrderSearchDTO.orgCode!= '' ">
                and org_code = #{serviceOrderSearchDTO.orgCode}
            </if>
            <if test="serviceOrderSearchDTO.orgParentCode!=null and serviceOrderSearchDTO.orgParentCode!= '' ">
                and org_parent_code = #{serviceOrderSearchDTO.orgParentCode}
            </if>
            <if test="serviceOrderSearchDTO.telephone!=null and serviceOrderSearchDTO.telephone!= '' ">
                and telephone = #{serviceOrderSearchDTO.telephone}
            </if>
            <if test="serviceOrderSearchDTO.repayStatus!=null or serviceOrderSearchDTO.repayStatus== 0 ">
                and repay_status = #{serviceOrderSearchDTO.repayStatus}
            </if>
            <if test="serviceOrderSearchDTO.providerName!=null and serviceOrderSearchDTO.providerName!= '' ">
                and provider_name = #{serviceOrderSearchDTO.providerName}
            </if>
            <if test="serviceOrderSearchDTO.paymentNo!=null and serviceOrderSearchDTO.paymentNo!= '' ">
                and payment_no = #{serviceOrderSearchDTO.paymentNo}
            </if>
        </where>
    </sql>
    <select id="selectServiceOrderInfoVOV2" resultType="com.zhongyixin.resale.biz.common.output.ServiceOrderInfoVOV2">
        <include refid="selectServiceOrderVO"/>
        <include refid="serviceOrderConditionV2"/>
        order by tran_date desc, create_time desc
    </select>
    <select id="statisticsServiceOrderTotalDataV2"
            resultType="com.zhongyixin.resale.biz.common.output.ServiceOrderStatisticsDataVO">
        select count(*) total_count,sum(amount) total_amount, sum(round(ifnull(amount,0) * (1 + convert(ifnull(sunshine_service_charge,0),decimal(16,6))/100),2)) total_tax,
        sum(round(amount*ifnull((datediff(case when end_rate_date is null THEN now() ELSE end_rate_date end,start_rate_date)+1),0)*0.0003,2)) total_interest
        from tb_service_order
        <include refid="serviceOrderConditionV2"/>
    </select>

    <select id="selectAllocationSecondOrder"
            resultType="com.zhongyixin.resale.biz.common.input.AllocationServiceOrderDto">
        select * from
        (select so.org_parent_code orgCode, count(*) orderCount, sum(so.amount) orderAmount
        from tb_service_order so left join tb_order o on so.order_id = o.card_order_no
        where so.channel = 1 and so.status in (0,9)
        and ((o.card_order_no is null)
        or (o.order_type = 'ygbh' and o.status = 9))
        and so.create_time &lt;= DATE_FORMAT( DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-%d 23:59:59')
        <if test="orgParentCode != null and orgParentCode != ''">
            and so.org_parent_code = #{orgParentCode}
        </if>
        <if test="orderMonth != null and orderMonth != ''">
            and so.create_time &gt;= STR_TO_DATE(CONCAT(#{orderMonth}, '-01'), '%Y-%m-%d')
            and so.create_time &lt; DATE_ADD(STR_TO_DATE(CONCAT(#{orderMonth}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        </if>
        <if test="orderStartTime != null and orderEndTime != null">
            and so.create_time between DATE_FORMAT( #{orderStartTime}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{orderEndTime}, '%Y-%m-%d 23:59:59')
        </if>
        GROUP BY so.org_parent_code) t;
    </select>
    <select id="selectAllocationThirdOrder"
            resultType="com.zhongyixin.resale.biz.common.input.AllocationServiceOrderDto">
        select * from
        (select so.org_code orgCode, count(*) orderCount, sum(so.amount) orderAmount
        from tb_service_order so left join tb_order o on so.order_id = o.card_order_no
        where so.channel = 1 and so.status in (0,9)
        and ((o.card_order_no is null)
        or (o.order_type = 'ygbh' and o.status = 9))
        and so.create_time &lt;= DATE_FORMAT( DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-%d 23:59:59')
        <if test="orgParentCode != null and orgParentCode != ''">
            and so.org_parent_code = #{orgParentCode}
        </if>
        <if test="orgCode != null and orgCode != ''">
            and so.org_code = #{orgCode}
        </if>
        <if test="orderMonth != null and orderMonth != ''">
            and so.create_time &gt;= STR_TO_DATE(CONCAT(#{orderMonth}, '-01'), '%Y-%m-%d')
            and so.create_time &lt; DATE_ADD(STR_TO_DATE(CONCAT(#{orderMonth}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        </if>
        <if test="orderStartTime != null and orderEndTime != null">
            and so.create_time between DATE_FORMAT( #{orderStartTime}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{orderEndTime}, '%Y-%m-%d 23:59:59')
        </if>
        GROUP BY so.org_code) t;
    </select>

    <select id="selectAllocationWaitOrder" resultType="com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO">
        select so.*
        from tb_service_order so left join tb_order o on so.order_id = o.card_order_no
        where so.channel = 1 and so.status in (0,9)
        and ((o.card_order_no is null)
        or (o.order_type = 'ygbh' and o.status = 9))
        and so.create_time &lt;= DATE_FORMAT( DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y-%m-%d 23:59:59')
        <if test="orgParentCode != null and orgParentCode != ''">
            and so.org_parent_code = #{orgParentCode}
        </if>
        <if test="orgCode != null and orgCode != ''">
            and so.org_code = #{orgCode}
        </if>
    </select>
    <select id="getTotalByOrderIds" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            tb_service_order
        WHERE
          order_id IN ( SELECT DISTINCT sunshine_order_id FROM tb_sunshine_repayment_file_data WHERE file_sign = #{fileSign} )
          and repay_status = 3
    </select>
    <select id="selectWaitPushOrder" resultType="com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO">
        select * from tb_service_order
        <where>
            <if test="startTime!=null and endTime!= null ">
                and tran_date between DATE_FORMAT( #{startTime}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{endTime}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="orderMode != null">
                and order_mode = #{orderMode}
            </if>
        </where>
    </select>
    <select id="getTaxTotalAmount" resultType="java.math.BigDecimal">
        SELECT
            sum(round(amount*(convert(sunshine_service_charge,decimal(10,6))/100+1),2))
        FROM
            tb_service_order
        WHERE
            order_id IN ( SELECT DISTINCT sunshine_order_id FROM tb_sunshine_repayment_file_data WHERE file_sign = #{fileSign} )
    </select>

    <select id="selectListByProvider" resultType="com.zhongyixin.resale.biz.entity.ServiceOrder">
        select
            *
        from
            tb_service_order
        where
            tran_date between concat(#{paytime},' 00:00:00') and concat(#{paytime},' 23:59:59' )
          and status = 2 and provider_name = #{provider}
    </select>

</mapper>
