<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.PayRecordMapper">

    <select id="selectSunshinePayRecordList"
            resultType="com.zhongyixin.resale.biz.common.output.SunshinePayRecordVo">
        select
               tbpr.id,tbo.card_order_no orderId,tbo.user_name userName,tbpr.telephone telephone,
               tbpr.bank_card_no bankCardNo,tbpr.amount amount,tbpr.create_time createTime,
               tbpr.`status` status,tbpr.trans_result transResult,tbpr.pay_company payCompany
        from tb_order tbo, tb_pay_record tbpr,tb_service_order tso
        where
              tbo.order_id = tbpr.order_id and tbo.card_order_no = tso.order_id
          and tbpr.status in (1,2,9,10) and tbpr.api_source = 'RONGZHI'
        <if test="sunshinePayRecordSearchDTO.orderId != null and sunshinePayRecordSearchDTO.orderId != ''">
            and tbo.card_order_no = #{sunshinePayRecordSearchDTO.orderId}
        </if>
        <if test="sunshinePayRecordSearchDTO.telephone != null and sunshinePayRecordSearchDTO.telephone != ''">
            and tbpr.telephone = #{sunshinePayRecordSearchDTO.telephone}
        </if>
        <if test="sunshinePayRecordSearchDTO.userName != null and sunshinePayRecordSearchDTO.userName != ''">
            and tbo.user_name like concat('%',#{sunshinePayRecordSearchDTO.userName},'%')
        </if>
        ORDER BY tbpr.create_time desc
    </select>
    <select id="selectNonClosedLoopPayRecordList"
            resultType="com.zhongyixin.resale.biz.common.output.NonClosedLoopPayRecordVo">
        select
        tbpr.id,tbo.user_name userName,tbpr.telephone telephone,
        tbo.plate_num plateNum,tbo.coupon_no couponNo,tbo.api_source apiSource,
        tbpr.bank_card_no bankCardNo,tbpr.amount amount,tbpr.create_time createTime,
        tbpr.`status` status,tbpr.trans_result transResult ,tbpr.pay_company payCompany
        from tb_order tbo, tb_pay_record tbpr
        where
        tbo.order_id = tbpr.order_id
        and tbpr.status in (1,2,9,10) and tbpr.api_source != 'RONGZHI'
        <if test="nonClosedLoopPayRecordSearchDTO.telephone != null and nonClosedLoopPayRecordSearchDTO.telephone != ''">
            and tbpr.telephone = #{nonClosedLoopPayRecordSearchDTO.telephone}
        </if>
        <if test="nonClosedLoopPayRecordSearchDTO.userName != null and nonClosedLoopPayRecordSearchDTO.userName != ''">
            and tbo.user_name like concat('%',#{nonClosedLoopPayRecordSearchDTO.userName},'%')
        </if>
        <if test="nonClosedLoopPayRecordSearchDTO.apiSource != null and nonClosedLoopPayRecordSearchDTO.apiSource != ''">
            and tbo.api_source = #{nonClosedLoopPayRecordSearchDTO.apiSource}
        </if>
        <if test="nonClosedLoopPayRecordSearchDTO.plateNum != null and nonClosedLoopPayRecordSearchDTO.plateNum != ''">
            and tbo.plate_num = #{nonClosedLoopPayRecordSearchDTO.plateNum}
        </if>
        <if test="nonClosedLoopPayRecordSearchDTO.couponNo != null and nonClosedLoopPayRecordSearchDTO.couponNo != ''">
            and tbo.coupon_no = #{nonClosedLoopPayRecordSearchDTO.couponNo}
        </if>
<!--        <if test="nonClosedLoopPayRecordSearchDTO.insurancestr != null and nonClosedLoopPayRecordSearchDTO.insurancestr != ''">-->
<!--            and tbo.insurancestr = #{nonClosedLoopPayRecordSearchDTO.insurancestr}-->
<!--        </if>-->
        ORDER BY tbpr.create_time desc
    </select>
    <select id="selectLastTimeOne" resultType="com.zhongyixin.resale.biz.entity.PayRecord">
        select * from tb_pay_record where status = #{status} and order_id = #{orderId}
        order by create_time desc limit 1
    </select>
</mapper>
