<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SinosigOrderInfoMapper">

    <update id="updateRepayInfoBySinosigOrderId">
        update sinosig_order_info
        set repay_id = #{repayId},
            end_rate_date= #{endRateDate}
        <where>
             sinosig_order_id in
             <foreach collection="sinosigOrderIds" item="sinosigOrderId" close=")" open="(" separator=",">
                 #{sinosigOrderId}
             </foreach>
        </where>
    </update>

    <select id="selectSinosigOrderIds" resultType="java.lang.String">
        SELECT DISTINCT sunshine_order_id FROM tb_sunshine_repayment_detail
        WHERE repay_batch_no = #{repayBatchNo}
        limit #{pageNum},#{pageSize}
    </select>

    <update id="updateStartRateInfoBySinosigOrderId">
        update sinosig_order_info
        set start_rate_date = #{startRateDate}
        where sinosig_order_id = #{sinosigOrderId}
    </update>
    <select id="selectAlipayServiceOrder" resultType="com.zhongyixin.resale.biz.common.input.SinosigOrderCopyDTO">
        select so.*,o.order_id orderNo from alipay_push_order apo
            inner join tb_order o on apo.order_id = o.order_id
            inner join tb_service_order so on so.order_id = o.card_order_no
        where  apo.deleted = 0
    </select>
    <select id="selectByOrderId" resultType="com.zhongyixin.resale.biz.entity.SinosigOrderInfo">
        select * from sinosig_order_info where sinosig_order_id = #{sinosigOrderId} and deleted = 0
    </select>

    <select id="selectOneByOrderId" resultType="com.zhongyixin.resale.biz.entity.SinosigOrderInfo">
        select * from sinosig_order_info where order_id = #{orderId}  and deleted = 0
    </select>
    <select id="selectOrderIdsCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT sunshine_order_id) FROM tb_sunshine_repayment_detail
        WHERE repay_batch_no = #{repayBatchNo}
    </select>

</mapper>
