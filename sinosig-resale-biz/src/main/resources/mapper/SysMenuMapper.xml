<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysMenuMapper">

    <sql id="selectMenu">
        select
            menu_id,menu_name,parent_id,order_num,path,redirect,component,is_iframe,is_cache,is_affix,is_link,menu_type,
            visible,status,perms,url,icon,create_by,create_time,update_by,update_time,remark
        from
            tb_sys_menu
    </sql>

    <select id="selectAuthMenuByUserId" resultType="com.zhongyixin.resale.biz.entity.SysMenu">
            select  distinct
                m.menu_id,m.menu_name,m.parent_id,m.order_num,m.path,m.redirect,m.component,m.is_iframe,m.is_cache,m.is_affix,
                m.menu_type,m.visible,m.is_link,m.perms,m.icon
            from tb_sys_menu m
                     left join tb_sys_role_menu rm on m.menu_id = rm.menu_id
                     left join tb_sys_user_role ur on rm.role_id = ur.role_id
                     left join tb_sys_role ro on ur.role_id = ro.role_id
                     left join tb_sys_user u on ur.user_id = u.user_id
            where ur.user_id = #{userId}
              and m.menu_type in ('M', 'C')
              and m.status = 0
              and ro.status = 0
            order by m.parent_id, m.order_num
    </select>
    <select id="selectAuthMenuByAdmin" resultType="com.zhongyixin.resale.biz.entity.SysMenu">
        select  distinct
            m.menu_id,m.menu_name,m.parent_id,m.order_num,m.path,m.redirect,m.component,m.is_iframe,m.is_cache,m.is_affix,
            m.menu_type,m.visible,m.is_link,m.perms,m.icon
        from tb_sys_menu m
        where  m.menu_type in ('M','C') and m.status = 0
        order by m.parent_id , m.order_num
    </select>
</mapper>
