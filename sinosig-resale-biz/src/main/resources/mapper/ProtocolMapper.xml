<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.ProtocolMapper">

    <select id="getHTMLContent" parameterType="com.zhongyixin.resale.biz.entity.Protocol" resultType="java.lang.String">
        select
            content
        from tb_protocol
        where
            api_source = #{apiSource} and status = #{status}
        order by id desc
        limit 1
    </select>
</mapper>
