<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SinosigNotifyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhongyixin.resale.biz.entity.SinosigNotifyRecord">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="notify_params" property="notifyParams" />
        <result column="response_params" property="responseParams" />
        <result column="exception_message" property="exceptionMessage" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, notify_params, response_params, exception_message, create_time
    </sql>

</mapper>
