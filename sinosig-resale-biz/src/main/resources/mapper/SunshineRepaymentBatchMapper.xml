<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SunshineRepaymentBatchMapper">

    <sql id="selectSunshineRepaymentBatch">
        select id,repay_batch_no,plat_repayment_no,file_sign,upload_by,oss_path,total_amount,
               total_count,org_code,org_name, status,association_status,create_time,end_rate_date,retry_count,memo,remark,tax_total_amount
        from
             tb_sunshine_repayment_batch
    </sql>
    <update id="updateFlowRepayStatus">
        update
            tb_sunshine_repayment_batch
        set status = #{paramStatus},association_status = 2
        <if test="endRateDay != null and endRateDay != ''">
            ,end_rate_date = #{endRateDay}
        </if>
        <if test="platRepaymentNo != null and platRepaymentNo != ''">
            ,plat_repayment_no = #{platRepaymentNo}
        </if>
        <if test="memo != null and memo != ''">
            ,memo = #{memo}
        </if>
        where
              repay_batch_no = #{repayBatchNo}
              and status = #{conditionStatus}
    </update>
    <update id="updateAssociationStatus">
        update
           tb_sunshine_repayment_batch
        set association_status = #{paramAssociationStatus},retry_count = retry_count + 1
        where
           repay_batch_no = #{repayBatchNo}
    </update>

    <update id="chageBatchEndDate">
        update tb_sunshine_repayment_batch set end_rate_date = #{endDate} where repay_batch_no = #{repayBatchNo}
    </update>
    <update id="chageServiceOrderEndDate">
        update tb_service_order set end_rate_date = #{endDate}
                                where order_id in
                                <foreach collection="sunshineOrderIds" item="orderId" separator="," open="(" close=")" >
                                    #{orderId}
                                </foreach>
    </update>

    <select id="queryPageList" resultType="com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch">
        <include refid="selectSunshineRepaymentBatch"/>
        <where>
            <if test="sunshineRepaymentBatchSearchDTO.status != null">
                and status = #{sunshineRepaymentBatchSearchDTO.status}
            </if>
            <if test="sunshineRepaymentBatchSearchDTO.repaymentId != null and sunshineRepaymentBatchSearchDTO.repaymentId != ''">
                and repay_batch_no = #{sunshineRepaymentBatchSearchDTO.repaymentId}
            </if>
            <if test="sunshineRepaymentBatchSearchDTO.createStartDate != null and sunshineRepaymentBatchSearchDTO.createEndDate != null">
                and create_time between DATE_FORMAT( #{sunshineRepaymentBatchSearchDTO.createStartDate}, '%Y-%m-%d 00:00:00') and DATE_FORMAT( #{sunshineRepaymentBatchSearchDTO.createEndDate}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="sunshineRepaymentBatchSearchDTO.secondOrgCode != null and sunshineRepaymentBatchSearchDTO.secondOrgCode != ''">
                and left(org_code,2) = left(#{sunshineRepaymentBatchSearchDTO.secondOrgCode},2)
            </if>
            <if test="sunshineRepaymentBatchSearchDTO.threeOrgCode != null and sunshineRepaymentBatchSearchDTO.threeOrgCode != ''">
                and org_code = #{sunshineRepaymentBatchSearchDTO.threeOrgCode}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectOrgCodeByRepayBatchNo" resultType="java.lang.String">
        select
             concat(left(org_code,2),'000000')
        from
             tb_sunshine_repayment_batch
        where repay_batch_no = #{repayBatchNo}
    </select>
    <select id="selectOneByRepayBatchNo"
            resultType="com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch">
        <include refid="selectSunshineRepaymentBatch"/>
        where repay_batch_no = #{repayBatchNo}
    </select>
    <select id="selectOneByPlatRepaymentNo"
            resultType="com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch">
        <include refid="selectSunshineRepaymentBatch"/>
        where plat_repayment_no = #{platRepaymentNo}
    </select>
    <select id="selectRetryRepaymentBatchList"
            resultType="com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch">
        <include refid="selectSunshineRepaymentBatch"/>
        where status = 2 and association_status in (8,9) and retry_count &lt;= 5
    </select>
    <select id="selectAssociationRepaymentBatchList"
            resultType="com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch">
        <include refid="selectSunshineRepaymentBatch"/>
        where status = 2 and create_time &lt; DATE_SUB(NOW(), INTERVAL 2 HOUR)
    </select>
    <select id="selectSunshineRepaymentOrderVOList"
            resultType="com.zhongyixin.resale.biz.common.output.SunshineRepaymentOrderVO">
        select repay_id repayment_id,order_id,amount,user_name,telephone,tran_date,org_parent_code,
               org_code,provider_name,end_rate_date,ifnull(datediff(case when end_rate_date is null THEN now() ELSE end_rate_date end,tran_date),0) as advance_fund_Day,
               round(amount*ifnull((datediff(case when end_rate_date is null THEN now() ELSE end_rate_date end,tran_date)+1),0)*0.0003,2) as interest
        from tb_service_order
        where order_id in
              (select distinct sunshine_order_id
               from tb_sunshine_repayment_detail
               where repay_batch_no = #{repaymentId})
    </select>
    <select id="selectSunshineOrderId" resultType="java.lang.String">
        select distinct sunshine_order_id
        from tb_sunshine_repayment_detail
        where repay_batch_no = #{repayBatchNo}
    </select>
</mapper>
