<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysDeptMapper">

    <select id="selectDataPermissionByUserId" resultType="com.zhongyixin.resale.biz.common.input.DataScopeDTO">
        select  distinct
            d.ancestors as ancestors,dr.data_scope as dataScope,dr.data_role_sort
        from tb_sys_dept d
             left join tb_sys_user_dept ud on ud.dept_id = d.dept_id
             left join tb_sys_user_data_role udr on udr.user_id = ud.user_id
             left join tb_sys_data_role dr on dr.data_role_id = udr.data_role_id
        where  ud.user_id = #{userId} and d.status = 0
        order by dr.data_role_sort
        limit 1
    </select>

    <select id="findSecondOrgAncestors" resultType="java.lang.String">
        SELECT CONCAT(org_parent_code,",",GROUP_CONCAT(org_code SEPARATOR ',')) from tb_organize_ygry WHERE org_parent_code = #{orgCode}
    </select>

    <select id="findDeptByDeptName" resultType="com.zhongyixin.resale.biz.entity.SysDept">
        select * from tb_sys_dept where dept_name = #{deptName} and del_flag = 0
    </select>

    <select id="findSecondOrgName" resultType="java.lang.String">
        SELECT org_name from tb_organize_ygry WHERE org_code = #{orgCode}
    </select>

</mapper>
