<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.NotifyJobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhongyixin.resale.biz.entity.NotifyJob">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="business_code" property="businessCode" />
        <result column="status" property="status" />
        <result column="count" property="count" />
        <result column="params" property="params" />
        <result column="ended" property="ended" />
        <result column="end_time" property="endTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, business_code, status, count, params, ended, end_time, create_time, update_time, deleted
    </sql>


    <select id="queryWaitScheduled" resultType="com.zhongyixin.resale.biz.entity.NotifyJob">
        select * from notify_job where status in (1,10) and ended = 0 and deleted = 0 and date_add(now(),INTERVAL 15 SECOND) >= next_time
    </select>


    <update id="updateNotifyJob">
        update notify_job
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            count = #{count},
            next_time = #{nextTime},
            <if test="ended != null">
                ended = #{ended},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark}
            </if>
        </set>
        <where>
            id = #{id} and status in(1,10)
        </where>
    </update>


    <select id="queryJobByCodeAndOrderId" resultType="com.zhongyixin.resale.biz.entity.NotifyJob">
        select * from  notify_job where business_code = #{code} and order_id = #{orderId} and deleted = 0
    </select>

    <select id="selectOneByBusinessCodeAndOrderId" resultMap="BaseResultMap">
        select * from notify_job
        where business_code = #{businessCode} and order_id = #{orderId} and deleted = 0
    </select>
</mapper>
