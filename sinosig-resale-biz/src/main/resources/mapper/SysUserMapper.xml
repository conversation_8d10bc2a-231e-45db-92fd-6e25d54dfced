<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysUserMapper">

    <sql id="selectSysUser">
        select
           user_id,user_name,user_password,user_type,email,telephone,sex,avatar,status,del_flag,
           login_ip,login_date,create_by,create_time,update_by,update_time,remark
        from
           tb_sys_user
    </sql>

    <select id="getByUsername" resultType="com.zhongyixin.resale.biz.entity.SysUser">
        <include refid="selectSysUser"/>
        <where>
            user_name = #{userName}
        </where>
    </select>
    <select id="getByUserId" resultType="com.zhongyixin.resale.biz.entity.SysUser">
        <include refid="selectSysUser"/>
        <where>
            user_id = #{userId}
        </where>
    </select>
</mapper>
