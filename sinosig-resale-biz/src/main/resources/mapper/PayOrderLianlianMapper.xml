<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.PayOrderLianlianMapper">

    <sql id="selectPayOrderLianlian">
        select
            id,pay_order_id,payment_order_no,order_id,coupon_no,card_order_no,card_name,mobile_number,
            insurancestr, card_no,plate_num,money_order,status,create_time, update_time,
            api_source, order_type,settle_date,info_order,memo,ext1, version
        from
            tb_pay_order_lianlian
    </sql>

    <select id="selectPayOrderLianlianByPayOrderId"
            resultType="com.zhongyixin.resale.biz.entity.PayOrderLianlian">
        <include refid="selectPayOrderLianlian"/>
        <where>
            pay_order_id = #{payOrderId}
        </where>
    </select>

    <select id="selectOrderByTenMinutes" resultType="com.zhongyixin.resale.biz.entity.PayOrderLianlian">
        <include refid="selectPayOrderLianlian"/>
        <where>
        status = 1
        and create_time &lt; DATE_SUB(now(),interval 10 MINUTE)
        </where>
    </select>
    <select id="selectUnfinishedOrderByOrderId" resultType="com.zhongyixin.resale.biz.entity.PayOrderLianlian">
        <include refid="selectPayOrderLianlian"/>
        <where>
            order_id = #{orderId} and status in(0,1)
        </where>
    </select>
    <select id="selectPayingOrPaidOrderByOrderId"
            resultType="com.zhongyixin.resale.biz.entity.PayOrderLianlian">
        <include refid="selectPayOrderLianlian"/>
        <where>
            order_id = #{orderId} and status in(1,2)
        </where>
    </select>

    <update id="updatePayOrderLianlianByPayOrderId">
        update tb_pay_order_lianlian
        <trim prefix="SET" suffixOverrides=",">
            <if test="settleDate != null and settleDate != ''">
                settle_date = #{settleDate},
            </if>
            <if test="paymentOrderNo != null and paymentOrderNo != ''">
                payment_order_no = #{paymentOrderNo},
            </if>
            <if test="infoOrder != null and infoOrder != ''">
                info_order = #{infoOrder},
            </if>
            <if test="memo != null and memo != ''">
                memo = #{memo},
            </if>
            <if test="status != null and status != ''">
                status = #{status}
            </if>
        </trim>
        where pay_order_id = #{payOrderId} and status = 1
    </update>
</mapper>
