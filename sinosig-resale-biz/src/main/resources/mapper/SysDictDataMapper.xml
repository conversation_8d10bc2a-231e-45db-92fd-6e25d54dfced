<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysDictDataMapper">

    <sql id="selectDictData">
        select
            id,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,
            create_by,create_time,update_by,update_time,remark
        from
            tb_sys_dict_data
    </sql>

    <select id="selectCarCompanyDictDataByType" resultType="com.zhongyixin.resale.biz.entity.SysDictData">
        <include refid="selectDictData"/>
        <where>
            status = 0 and dict_type = #{dictType}
        </where>
        order by dict_sort
    </select>
    <select id="selectDictDataByType" resultType="com.zhongyixin.resale.biz.entity.SysDictData">
        <include refid="selectDictData"/>
        <where>
            status = 0 and dict_type = #{dictType}
        </where>
        order by dict_sort
    </select>
    <select id="selectDictDataByTypeAndLabel" resultType="com.zhongyixin.resale.biz.entity.SysDictData">
        <include refid="selectDictData"/>
        <where>
            status = 0 and dict_type = #{dictType} and dict_label = #{dictLabel}
        </where>
        order by id limit 1
    </select>
</mapper>
