<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysRoleMapper">


    <select id="selectRolePermissionByUserId" resultType="com.zhongyixin.resale.biz.entity.SysRole">
        select
            distinct r.role_id,r.role_name,r.role_key,r.remark,r.create_time,r.status
        from tb_sys_role r
            left join tb_sys_user_role ur on ur.role_id = r.role_id
        where r.status = 0
           and ur.user_id = #{userId}
    </select>
</mapper>
