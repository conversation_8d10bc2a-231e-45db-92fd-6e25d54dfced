<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OrderWriteOffMapper">
    <update id="updateVasCouponStatus">
        update  order_write_off set vas_coupon_status = #{orderWriteOff.vasCouponStatus}
        where id = #{orderWriteOff.id} and vas_coupon_status in
        <foreach collection="originalStatusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </update>


    <select id="selectOneByOrderId" resultType="com.zhongyixin.resale.biz.entity.OrderWriteOff">
        select * from order_write_off where order_id = #{orderId} and deleted = 0
    </select>
</mapper>
