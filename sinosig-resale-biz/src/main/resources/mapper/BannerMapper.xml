<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.BannerMapper">

    <select id="selectListByType" resultType="com.zhongyixin.resale.biz.entity.Banner">
        select * from banner where deleted = 0 and status = #{status} and type = #{type} and api_source = #{apiSource}
        order by sort
    </select>
    <select id="selectListByStatus" resultType="com.zhongyixin.resale.biz.entity.Banner">
        select * from banner where deleted = 0 and status = #{status}  and api_source = #{apiSource} order by sort
    </select>
    <select id="selectListByTypes" resultType="com.zhongyixin.resale.biz.entity.Banner">
        select * from banner where deleted = 0 and status = #{status} and api_source = #{apiSource} and type in (
          <foreach collection="type" separator="," item="item" index="index">
                #{item}
          </foreach>)
        order by sort
    </select>


</mapper>
