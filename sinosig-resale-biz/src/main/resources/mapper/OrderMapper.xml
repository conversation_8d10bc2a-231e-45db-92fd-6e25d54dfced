<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OrderMapper">
    <sql id="selectOrder">
        select
            id,order_id,create_time,tran_date,api_source,order_type,card_order_no,pay_company,
            coupon_no,user_name,telphone,id_card,plate_num,insurancestr,bank_card_no,payment_no,
            point,amount,uuid,count,pay_way,status,redirect_url,fail_explain,version
        from
            tb_order
    </sql>

    <select id="selectOrderByApiSource" resultType="com.zhongyixin.resale.biz.entity.Order">
        <include refid="selectOrder"/>
        <where>
            <if test="apiSource != null and apiSource != ''">
               and api_source = #{apiSource}
            </if>
            <if test="orderType != null and orderType != ''">
                and order_type = #{orderType}
            </if>
            <if test="cardOrderNo != null and cardOrderNo != ''">
                and card_order_no = #{cardOrderNo}
            </if>
            <if test="couponNo != null and couponNo != ''">
                and coupon_no = #{couponNo}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="telphone != null and telphone != ''">
                and telphone = #{telphone}
            </if>
            <if test="idCard != null and idCard != ''">
                and id_card = #{idCard}
            </if>
            <if test="plateNum != null and plateNum != ''">
                and plate_num = #{plateNum}
            </if>
            <if test="insurancestr != null and insurancestr != ''">
                and insurancestr = #{insurancestr}
            </if>
            <if test="bankCardNo != null and bankCardNo != ''">
                and bank_card_no = #{bankCardNo}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by id desc limit 1
    </select>

    <select id="selectFailAndInitOrderByApiSource" resultType="com.zhongyixin.resale.biz.entity.Order">
        <include refid="selectOrder"/>
        <where>
            <if test="apiSource != null and apiSource != ''">
                and api_source = #{apiSource}
            </if>
            <if test="orderType != null and orderType != ''">
                and order_type = #{orderType}
            </if>
            <if test="cardOrderNo != null and cardOrderNo != ''">
                and card_order_no = #{cardOrderNo}
            </if>
            <if test="couponNo != null and couponNo != ''">
                and coupon_no = #{couponNo}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="telphone != null and telphone != ''">
                and telphone = #{telphone}
            </if>
            <if test="idCard != null and idCard != ''">
                and id_card = #{idCard}
            </if>
            <if test="plateNum != null and plateNum != ''">
                and plate_num = #{plateNum}
            </if>
            <if test="insurancestr != null and insurancestr != ''">
                and insurancestr = #{insurancestr}
            </if>
            <if test="bankCardNo != null and bankCardNo != ''">
                and bank_card_no = #{bankCardNo}
            </if>
                and status in (0,9)
        </where>
        order by id desc limit 1
    </select>

    <select id="selectOrderStatusWithPingAn" resultType="java.lang.String">
       select status from tb_order
       where
         api_source = #{apiSource}
         and order_type = #{orderType}
         and coupon_no = #{couponNo}
    </select>
    <select id="selectOrderStatusWithGuoRen" resultType="java.lang.String">
        select status from tb_order
        where
            api_source = #{apiSource}
          and order_type = #{orderType}
          and telphone = #{telphone}
    </select>
    <select id="selectOrderStatusWithChannel" resultType="java.lang.String">
        select status from tb_order
        where
            api_source = #{apiSource}
          and order_type = #{orderType}
          and coupon_no = #{couponNo}
    </select>
    <select id="selectOrderStatusWithSunshine" resultType="java.lang.String">
        select status from tb_order
        where
            api_source = #{apiSource}
          and order_type = #{orderType}
          and card_order_no = #{cardOrderNo}
    </select>

    <select id="selectOrderByOrderId" resultType="com.zhongyixin.resale.biz.entity.Order">
        <include refid="selectOrder"/>
        <where>
            order_id = #{orderId}
        </where>
    </select>
    <select id="selectOrderByCardOrderNo" resultType="com.zhongyixin.resale.biz.entity.Order">
        <include refid="selectOrder"/>
        <where>
            card_order_no = #{orderId}
        </where>
    </select>



    <select id="selectListByStatus" resultType="com.zhongyixin.resale.biz.entity.Order">
        select * from tb_order where status = #{status} and  pay_way = '支付宝'
    </select>
    <select id="selectNotPushRecordData" resultType="com.zhongyixin.resale.biz.entity.Order">
        select o.*
        from tb_order o
        left join vas_order_publish_record r on o.order_id = r.order_id
        where r.id is null and pay_way = '支付宝'
    </select>


    <update id="updateOrderById">
        update tb_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="plateNum != null and plateNum != ''">
                plate_num = #{plateNum},
            </if>
            <if test="telphone != null and telphone != ''">
                telphone = #{telphone},
            </if>
            <if test="payCompany != null and payCompany != ''">
                pay_company = #{payCompany},
            </if>
            <if test="payWay != null and payWay != ''">
                pay_way = #{payWay},
            </if>
            <if test="bankCardNo != null and bankCardNo != ''">
                bank_card_no = #{bankCardNo},
            </if>
            <if test="uuid != null and uuid != ''">
                uuid = #{uuid},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="redirectUrl != null and redirectUrl != ''">
                redirect_url = #{redirectUrl},
            </if>
            <if test="orderType != null and orderType != ''">
                order_type = #{orderType},
            </if>
        </trim>
        where id = #{id} and status in (0,9)
    </update>

    <update id="updateOrderByIdAndDealing">
        update tb_order set status = #{status}
        where id = #{id} and status = 1
    </update>

    <update id="updateOrderUUIDById">
        update tb_order set uuid = #{uuid} where id = #{id}
    </update>

    <update id="updateOrderByOrderId">
        update tb_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="failExplain != null and failExplain != ''">
                fail_explain = #{failExplain},
            </if>
            <if test="tranDate != null">
                tran_date = #{tranDate},
            </if>
            <if test="paymentNo != null and paymentNo != ''">
                payment_no = #{paymentNo},
            </if>
            <if test="payWay != null and payWay != ''">
                pay_way = #{payWay},
            </if>
        </trim>
        where order_id = #{orderId} and status = 1
    </update>


    <update id="cancelOrder">
        update tb_order set status = 7 where card_order_no = #{orderId} and status = 0
    </update>

    <update id="updateOrder">
        update tb_order set user_name = #{modifyToName},id_card = #{modifyToIdNo} where card_order_no = #{orderId} and status in (0,9)
    </update>

    <update id="updateOrderPayWayByOrderId">
        update tb_order set pay_way = #{payWay} where order_id = #{orderId}
    </update>
</mapper>
