<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OrganizeYgryMapper">

    <select id="selectOrganize" resultType="com.zhongyixin.resale.biz.entity.OrganizeYgry">
        select
          id,org_code,org_name,org_parent_code,level,create_time,update_time,balance,
          status,total_amount,freeze_amount,quota_period,preserve_status,alipay_switch
        from
          tb_organize_ygry
        where org_code = #{orgCode}
    </select>
    <select id="selectOrganizeInfo" resultType="com.zhongyixin.resale.biz.common.output.OrganizeInfoVO">
        select
        o.id as id, o.org_code as orgCode ,o.org_name as orgName, ifnull(total.totalAmount,0) as totalAmount,
        ifnull(consume.consumeAmount,0) as consumeAmount,
        ifnull(total.totalAmount,0)-ifnull(consume.consumeAmount,0) as balance,
        concat(ROUND((ifnull(total.totalAmount,0) -ifnull(consume.consumeAmount,0)) /ifnull(total.totalAmount,0)*100,2),'%') as balanceRate,
        o.status as status,
        o.quota_period as quotaPeriod
        from tb_organize_ygry o
        left join
            (select org_code as org_code,sum(amount) consumeAmount
            from tb_org_consume
                <if test="organizeSearchDTO.createStartDate != null and organizeSearchDTO.createEndDate != null">
                    where
                    consume_time BETWEEN #{organizeSearchDTO.createStartDate} and #{organizeSearchDTO.createEndDate}
                </if>
            GROUP BY org_code) consume
        on o.org_code = consume.org_code
        left join
            (select org_code as org_code, sum(
                case when create_time =
                          (select MIN(create_time) from tb_add_fund_record
                            <if test="organizeSearchDTO.createStartDate != null and organizeSearchDTO.createEndDate != null">
                            where
                              consume_time BETWEEN #{organizeSearchDTO.createStartDate} and #{organizeSearchDTO.createEndDate}
                            </if>)
                Then after_amount ELSE amount end) as totalAmount
            from tb_add_fund_record
            <if test="organizeSearchDTO.createStartDate != null and organizeSearchDTO.createEndDate != null">
              where
                consume_time BETWEEN #{organizeSearchDTO.createStartDate} and #{organizeSearchDTO.createEndDate}
            </if>
            GROUP BY org_code) total
        on total.org_code = o.org_code
        where
            o.level = 2
            <if test="organizeSearchDTO.status != null">
              and o.status = #{organizeSearchDTO.status}
            </if>
            <if test="organizeSearchDTO.orgCode != null">
              and o.org_code = #{organizeSearchDTO.orgCode}
            </if>
    </select>

    <update id="changeOrganizeBanlance">
        update tb_organize_ygry
        set
            balance = balance + #{amount} ,
            total_amount = total_amount + #{amount}
        where id = #{id}
    </update>

    <select id="selectOrganizeSummarizeInfo" resultType="com.zhongyixin.resale.biz.common.output.OrganizeSummarizeVO">
        select o.*, toy.org_name as orgName,#{organizeSummarizeDTO.repayStatus} as repayStatus
        from (select tso.org_parent_code   as orgCode,
        ifnull(count(tso.amount), 0) as advanceFundCount,
        ifnull(sum(tso.amount), 0)   as amount,
        sum(round(ifnull(tso.amount,0) * (1 + convert(ifnull(tso.sunshine_service_charge,0),decimal(16,6))/100),2)) as taxAmount
        from tb_service_order tso
        where
        tso.repay_status = #{organizeSummarizeDTO.repayStatus}
        <if test="organizeSummarizeDTO.createStartDate != null and organizeSummarizeDTO.createEndDate != null">
            and tso.create_time between DATE_FORMAT( #{organizeSummarizeDTO.createStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{organizeSummarizeDTO.createEndDate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="organizeSummarizeDTO.tranStartDate != null and organizeSummarizeDTO.tranEndDate != null">
            and tso.tran_date between DATE_FORMAT( #{organizeSummarizeDTO.tranStartDate}, '%Y-%m-%d 00:00:00')  and  DATE_FORMAT( #{organizeSummarizeDTO.tranEndDate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="organizeSummarizeDTO.month != null and organizeSummarizeDTO.month != ''">
            and tso.tran_date &gt;= STR_TO_DATE(CONCAT(#{organizeSummarizeDTO.month}, '-01'), '%Y-%m-%d')
            and tso.tran_date &lt; DATE_ADD(STR_TO_DATE(CONCAT(#{organizeSummarizeDTO.month}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        </if>
        <if test="organizeSummarizeDTO.orgCode != null and organizeSummarizeDTO.orgCode != ''">
            and tso.org_parent_code = #{organizeSummarizeDTO.orgCode}
        </if>
        group by tso.org_parent_code
        ) o
        left join tb_organize_ygry toy on o.orgCode = toy.org_code
        order by o.orgCode
    </select>

    <select id="selectOrganizeSecondList" resultType="com.zhongyixin.resale.biz.entity.OrganizeYgry">
        select org_code,org_name from tb_organize_ygry where level = 2
    </select>
    <select id="selectOrganizeListByOrgParentCode"
            resultType="com.zhongyixin.resale.biz.entity.OrganizeYgry">
        select org_code,org_name from tb_organize_ygry where org_parent_code = #{orgCode}
    </select>

    <select id="selectNoPermissionOrganizeListByOrgParentCode"
            resultType="com.zhongyixin.resale.biz.entity.OrganizeYgry">
        select org_code,org_name from tb_organize_ygry where org_parent_code = #{orgCode}
    </select>
    <select id="selectTotalAmountByOrgCode" resultType="java.math.BigDecimal">
        select total_amount from tb_organize_ygry where level = 2 and status = 1 and org_code = #{orgCode}
    </select>
    <select id="getOrgInfo" resultType="com.zhongyixin.resale.biz.entity.OrganizeYgry">
        select oy.*,IFNULL(temp.start_rate_date,CURDATE()) as start_rate_date from tb_organize_ygry oy
        left join
        ( SELECT org_parent_code, min( start_rate_date ) start_rate_date FROM tb_service_order WHERE repay_status = 2 GROUP BY org_parent_code
        ) temp
        on oy.org_code = temp.org_parent_code
        <where>
            level = 2 
            <if test="orgSearchDTO.status != null">
                and status = #{orgSearchDTO.status}
            </if>
            <if test="orgSearchDTO.preserveStatus != null">
                and preserve_status = #{orgSearchDTO.preserveStatus}
            </if>
            <if test="orgSearchDTO.orgCode != null and orgSearchDTO.orgCode != ''">
                and oy.org_code = #{orgSearchDTO.orgCode}
            </if>
            <if test="orgSearchDTO.createStartDate != null">
                and create_time > #{orgSearchDTO.createStartDate}
            </if>
            <if test="orgSearchDTO.createEndDate != null">
                and create_time <![CDATA[ <= ]]> #{orgSearchDTO.createEndDate}
            </if>
        </where>
        order by
        start_rate_date
        <if test="orgSearchDTO.isAesByMaxAccountDays != null and orgSearchDTO.isAesByMaxAccountDays">
            desc
        </if>

    </select>

    <update id="frozenAmount">
        update tb_organize_ygry
        set freeze_amount = freeze_amount + #{amount},
            balance = balance - #{amount}
        where org_code = #{orgCode} and balance &gt;= #{amount} and level = 2 and status = 1
    </update>

    <update id="recoveryAmount">
        update tb_organize_ygry
        set freeze_amount = freeze_amount - #{amount},
            balance = balance + #{amount}
        where org_code = #{orgCode}  and freeze_amount &gt;= #{amount}
    </update>

    <update id="deductionAmount">
        update tb_organize_ygry
        set freeze_amount = freeze_amount - #{amount}
        where org_code = #{orgCode} and freeze_amount &gt;= #{amount}
    </update>

    <update id="safeguardOrganizeAccount">
        update tb_organize_ygry
        set total_amount = #{totalAmount},
            quota_period = #{quotaPeriod},
            balance = balance + #{amount}
        where id = #{id}
    </update>
    <update id="rechargeRepayAmount">
        update tb_organize_ygry
        set balance = balance + #{amount}
        where org_code = #{orgCode} and level = 2
    </update>
    <update id="updatePreserveStatus">
        update tb_organize_ygry
        set preserve_status = #{preserveStatus}
        where org_code = #{orgCode} and level = 2
    </update>
    <update id="updateAlipaySwitchByOrgParentCode">
        update tb_organize_ygry
        set alipay_switch = #{alipaySwitch}
        where org_parent_code = #{orgParentCode}
    </update>

    <update id="updateAlipaySwitchByOrgCode">
        update tb_organize_ygry
        set alipay_switch = #{alipaySwitch}
        where org_code = #{orgCode}
    </update>


    <select id="queryOrganizeInfo" resultType="com.zhongyixin.resale.biz.common.output.OrgOutput">
        select org_code,org_name from tb_organize_ygry
    </select>

    <select id="selectSecondAlipaySwitchVoIPage"
            resultType="com.zhongyixin.resale.biz.common.output.OrganizeAlipaySwitchVo">
        select org_code,org_parent_code,org_name,alipay_switch from tb_organize_ygry
                                 where level = 2
        <if test="orgParentCode != null and orgParentCode != ''">
           and org_code = #{orgParentCode}
        </if>
        <if test="alipaySwitch != null ">
            and alipay_switch = #{alipaySwitch}
        </if>
    </select>

    <select id="selectThirdAlipaySwitchVoIPage" resultType="com.zhongyixin.resale.biz.common.output.OrganizeAlipaySwitchVo">
        select org_code,org_parent_code,org_name,alipay_switch from tb_organize_ygry
        where level = 3
        <if test="orgParentCode != null and orgParentCode != ''">
            and org_parent_code = #{orgParentCode}
        </if>
        <if test="orgCode != null and orgCode != ''">
            and org_code = #{orgCode}
        </if>
        <if test="alipaySwitch != null ">
            and alipay_switch = #{alipaySwitch}
        </if>
    </select>

    <select id="selectCountByOrgParentCode" resultType="java.lang.Integer">
        select count(*) from tb_organize_ygry  where  org_parent_code = #{orgParentCode}
    </select>

    <select id="selectCountByOrgParentCodeAndAlipaySwitch" resultType="java.lang.Integer">
        select count(*) from tb_organize_ygry  where  org_parent_code = #{orgParentCode} and alipay_switch = #{alipaySwitch}
    </select>

</mapper>
