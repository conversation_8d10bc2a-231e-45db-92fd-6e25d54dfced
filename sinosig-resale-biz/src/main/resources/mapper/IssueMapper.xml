<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.IssueMapper">

    <select id="selectOneByApiSource" resultType="com.zhongyixin.resale.biz.entity.Issue">
        select
             id,api_source,create_time,issue_start_date,issue_end_date,issue_start_time,issue_end_time,status,order_type,auto_add_day,content
        from
             tb_issue
        where api_source = #{apiSource} and order_type = #{orderType} and status = 2
        order by create_time desc limit 1
    </select>
    <select id="selectListByAutoAddDay" resultType="com.zhongyixin.resale.biz.entity.Issue">
        select
            id,api_source,create_time,issue_start_date,issue_end_date,issue_start_time,issue_end_time,status,order_type,auto_add_day,content
        from
            tb_issue
        where status = 2 and auto_add_day = 1;
    </select>


</mapper>
