<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.SysRoleMenuMapper">

    <select id="getMenuPermissionByUserId" resultType="java.lang.String">
        select distinct m.perms from
        tb_sys_menu m
        left join tb_sys_role_menu rm on m.menu_id = rm.menu_id
        left join tb_sys_user_role ur on ur.role_id = rm.role_id
        left join tb_sys_role r on r.role_id= ur.role_id
        where m.status = 0 and r.status = 0 and ur.user_id = #{userId}
    </select>
    <select id="getAllMenuURL" resultType="com.zhongyixin.resale.biz.entity.SysMenu">
       <include refid="com.zhongyixin.resale.biz.mapper.SysMenuMapper.selectMenu"/>
       <where>
           status = 0 and url is not null
       </where>
    </select>
</mapper>
