<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.DownloadDataManageMapper">

    <select id="pageList" resultType="com.zhongyixin.resale.biz.entity.DownloadDataManage">
        select * from tb_download_data_manage
        <where>
            create_by = #{createBy,jdbcType=VARCHAR} and date_time &gt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                and date_time &gt;= date_format(#{startDate},'%Y-%m-%d 00:00:00') and date_time &lt;= date_format(#{endDate},'%Y-%m-%d 23:59:59')
            </if>
        </where>
        order by date_time desc
    </select>
    <select id="selectOneByBatchNo" resultType="com.zhongyixin.resale.biz.entity.DownloadDataManage">
        select * from tb_download_data_manage where batch_no = #{batchNo}
    </select>

    <update id="updateDownloadDataManageByBatchNo">
       update tb_download_data_manage set status = #{status}
        <if test="path != null and path != ''">
            , path = #{path}
        </if>
        <if test="remark != null and remark != ''">
            , remark = #{remark}
        </if>
        where batch_no = #{batchNo}
    </update>

</mapper>
